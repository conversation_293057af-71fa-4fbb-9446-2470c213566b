'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';
import { Button } from './ui/Button';

interface Game {
  id: string;
  name: string;
  status: 'waiting' | 'playing' | 'finished';
  gameMode: string;
  currentPlayers: number;
  maxPlayers: number;
  createdAt: string;
}

interface GameLobbyProps {
  onJoinGame: (gameId: string) => void;
  onCreateGame: () => void;
}

export const GameLobby: React.FC<GameLobbyProps> = ({
  onJoinGame,
  onCreateGame
}) => {
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'waiting' | 'playing'>('waiting');

  useEffect(() => {
    fetchGames();
  }, [filter]);

  const fetchGames = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/games?status=${filter === 'all' ? '' : filter}`);
      const data = await response.json();
      
      if (data.success) {
        setGames(data.data.games);
      }
    } catch (error) {
      console.error('获取游戏列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      waiting: '等待中',
      playing: '进行中',
      finished: '已结束'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      waiting: 'text-green-400',
      playing: 'text-yellow-400',
      finished: 'text-gray-400'
    };
    return colorMap[status as keyof typeof colorMap] || 'text-gray-400';
  };

  const getGameModeText = (mode: string) => {
    const modeMap = {
      standard_12: '12人标准场',
      advanced_12: '12人进阶场',
      custom: '自定义'
    };
    return modeMap[mode as keyof typeof modeMap] || mode;
  };

  const filteredGames = games.filter(game => {
    if (filter === 'all') return true;
    return game.status === filter;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🐺 LLM狼人杀游戏大厅
          </h1>
          <p className="text-gray-300 text-lg">
            观看不同AI模型的精彩对决
          </p>
        </div>

        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
          <div className="flex gap-2">
            <Button
              variant={filter === 'waiting' ? 'primary' : 'ghost'}
              onClick={() => setFilter('waiting')}
            >
              等待中
            </Button>
            <Button
              variant={filter === 'playing' ? 'primary' : 'ghost'}
              onClick={() => setFilter('playing')}
            >
              进行中
            </Button>
            <Button
              variant={filter === 'all' ? 'primary' : 'ghost'}
              onClick={() => setFilter('all')}
            >
              全部
            </Button>
          </div>

          <div className="flex gap-2">
            <Button onClick={fetchGames} variant="ghost">
              🔄 刷新
            </Button>
            <Button onClick={onCreateGame}>
              ➕ 创建游戏
            </Button>
          </div>
        </div>

        {/* 游戏列表 */}
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            <p className="text-white mt-4">加载中...</p>
          </div>
        ) : filteredGames.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-6xl mb-4">🎮</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                暂无游戏
              </h3>
              <p className="text-gray-400 mb-6">
                {filter === 'waiting' ? '当前没有等待中的游戏' : '没有找到相关游戏'}
              </p>
              <Button onClick={onCreateGame}>
                创建第一个游戏
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGames.map((game) => (
              <Card key={game.id} hover className="relative">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="truncate pr-2">
                      {game.name}
                    </CardTitle>
                    <span className={`text-sm font-medium ${getStatusColor(game.status)}`}>
                      {getStatusText(game.status)}
                    </span>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">游戏模式:</span>
                      <span className="text-white">{getGameModeText(game.gameMode)}</span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">玩家数量:</span>
                      <span className="text-white">
                        {game.currentPlayers}/{game.maxPlayers}
                      </span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">创建时间:</span>
                      <span className="text-white">
                        {new Date(game.createdAt).toLocaleString('zh-CN', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>

                    {/* 进度条 */}
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${(game.currentPlayers / game.maxPlayers) * 100}%`
                        }}
                      />
                    </div>

                    <div className="pt-2">
                      {game.status === 'waiting' ? (
                        <Button
                          className="w-full"
                          onClick={() => onJoinGame(game.id)}
                          disabled={game.currentPlayers >= game.maxPlayers}
                        >
                          {game.currentPlayers >= game.maxPlayers ? '房间已满' : '观战'}
                        </Button>
                      ) : game.status === 'playing' ? (
                        <Button
                          className="w-full"
                          variant="secondary"
                          onClick={() => onJoinGame(game.id)}
                        >
                          观战进行中
                        </Button>
                      ) : (
                        <Button
                          className="w-full"
                          variant="ghost"
                          onClick={() => onJoinGame(game.id)}
                        >
                          查看结果
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
