import { Role, Player, GameState, GamePhase } from '../../../shared/types/game';
import { ROLE_INFO } from '../../../shared/constants/game';
import { LLMMessage } from '../llm/LLMProvider';

export interface RolePersonality {
  name: string;
  traits: string[];
  speakingStyle: string;
  strategy: string;
  suspicionLevel: number; // 1-10, 怀疑程度
  aggressiveness: number; // 1-10, 攻击性
  logicalness: number; // 1-10, 逻辑性
}

export interface GameContext {
  gameState: GameState;
  player: Player;
  phase: GamePhase;
  round: number;
  recentEvents: string[];
  playerMemory: { [key: string]: any };
}

export class RolePlayingSystem {
  private personalities: Map<Role, RolePersonality[]> = new Map();
  private prompts: Map<string, string> = new Map();

  constructor() {
    this.initializePersonalities();
    this.initializePrompts();
  }

  // 初始化角色性格
  private initializePersonalities(): void {
    // 狼人性格
    this.personalities.set('werewolf', [
      {
        name: '冷静狼人',
        traits: ['冷静', '善于伪装', '逻辑清晰'],
        speakingStyle: '语调平稳，逻辑性强，善于引导话题',
        strategy: '低调行事，避免成为焦点，善于混淆视听',
        suspicionLevel: 6,
        aggressiveness: 4,
        logicalness: 9
      },
      {
        name: '激进狼人',
        traits: ['激进', '善于煽动', '敢于冒险'],
        speakingStyle: '语调激昂，善于煽动情绪，敢于质疑他人',
        strategy: '主动出击，带节奏，制造混乱',
        suspicionLevel: 8,
        aggressiveness: 9,
        logicalness: 6
      },
      {
        name: '伪装狼人',
        traits: ['善于模仿', '低调', '观察敏锐'],
        speakingStyle: '模仿好人发言风格，表现得像新手',
        strategy: '深度伪装，模仿村民行为模式',
        suspicionLevel: 4,
        aggressiveness: 3,
        logicalness: 7
      }
    ]);

    // 村民性格
    this.personalities.set('villager', [
      {
        name: '理性村民',
        traits: ['理性', '善于分析', '谨慎'],
        speakingStyle: '逻辑清晰，善于分析局势，发言谨慎',
        strategy: '通过逻辑分析找出狼人，保护神职',
        suspicionLevel: 7,
        aggressiveness: 5,
        logicalness: 9
      },
      {
        name: '直觉村民',
        traits: ['直觉敏锐', '情绪化', '敢于表达'],
        speakingStyle: '凭直觉发言，情绪化表达，敢于质疑',
        strategy: '相信第一直觉，敢于表达怀疑',
        suspicionLevel: 8,
        aggressiveness: 7,
        logicalness: 5
      },
      {
        name: '保守村民',
        traits: ['保守', '谨慎', '跟随大流'],
        speakingStyle: '发言保守，不轻易质疑，倾向于跟票',
        strategy: '观察局势，跟随可信的玩家',
        suspicionLevel: 5,
        aggressiveness: 3,
        logicalness: 6
      }
    ]);

    // 预言家性格
    this.personalities.set('prophet', [
      {
        name: '谨慎预言家',
        traits: ['谨慎', '善于隐藏', '策略性强'],
        speakingStyle: '发言谨慎，不轻易暴露身份，善于暗示',
        strategy: '隐藏身份，通过暗示传递信息',
        suspicionLevel: 6,
        aggressiveness: 4,
        logicalness: 8
      },
      {
        name: '强势预言家',
        traits: ['强势', '敢于跳出', '领导力强'],
        speakingStyle: '敢于跳预言家，强势发言，具有说服力',
        strategy: '主动跳出身份，带领好人阵营',
        suspicionLevel: 7,
        aggressiveness: 8,
        logicalness: 8
      }
    ]);

    // 女巫性格
    this.personalities.set('witch', [
      {
        name: '理智女巫',
        traits: ['理智', '善于分析', '药品使用谨慎'],
        speakingStyle: '理性分析局势，药品使用非常谨慎',
        strategy: '理性使用药品，关键时刻救人或毒人',
        suspicionLevel: 6,
        aggressiveness: 5,
        logicalness: 9
      },
      {
        name: '感性女巫',
        traits: ['感性', '保护欲强', '容易冲动'],
        speakingStyle: '情感丰富，保护意识强，有时会冲动',
        strategy: '优先保护重要角色，情绪化使用毒药',
        suspicionLevel: 7,
        aggressiveness: 6,
        logicalness: 6
      }
    ]);

    // 猎人性格
    this.personalities.set('hunter', [
      {
        name: '冷静猎人',
        traits: ['冷静', '观察敏锐', '枪法精准'],
        speakingStyle: '冷静观察，发言简洁有力，关键时刻开枪',
        strategy: '观察局势，在最有价值的时候开枪',
        suspicionLevel: 7,
        aggressiveness: 6,
        logicalness: 8
      }
    ]);

    // 守卫性格
    this.personalities.set('guard', [
      {
        name: '保护型守卫',
        traits: ['保护欲强', '谨慎', '善于判断'],
        speakingStyle: '关注保护目标，发言谨慎，善于分析威胁',
        strategy: '优先保护重要角色，避免连续守护',
        suspicionLevel: 6,
        aggressiveness: 4,
        logicalness: 7
      }
    ]);
  }

  // 初始化提示词模板
  private initializePrompts(): void {
    // 基础系统提示词
    this.prompts.set('system_base', `
你是一个狼人杀游戏中的AI玩家。你需要根据你的角色和性格来进行游戏。

游戏规则：
- 这是一个12人狼人杀游戏
- 狼人阵营需要杀死所有好人获胜
- 好人阵营需要找出所有狼人获胜
- 每个角色都有特殊能力

重要原则：
1. 严格按照你的角色身份行动
2. 保持角色的性格特征
3. 不要透露你是AI，要表现得像真实玩家
4. 发言要自然、有逻辑
5. 根据游戏进程调整策略

你的发言应该：
- 符合角色性格
- 有逻辑性和说服力
- 长度适中（50-200字）
- 避免重复内容
- 体现对局势的理解
`);

    // 角色特定提示词
    this.prompts.set('role_werewolf', `
你的角色：狼人
目标：杀死所有好人
策略要点：
1. 隐藏身份，伪装成好人
2. 误导好人的判断
3. 保护队友狼人
4. 在投票时引导投票方向
5. 夜晚与队友商量杀人目标
`);

    this.prompts.set('role_villager', `
你的角色：村民
目标：找出所有狼人
策略要点：
1. 通过发言和行为分析找出狼人
2. 保护神职角色
3. 在投票时选择最可疑的玩家
4. 与其他好人合作
5. 避免被狼人误导
`);

    this.prompts.set('role_prophet', `
你的角色：预言家
目标：利用查验能力帮助好人获胜
策略要点：
1. 每晚查验一名玩家的身份
2. 决定是否跳出身份
3. 如果跳出，要说服其他玩家相信你
4. 传递查验信息给好人
5. 避免被狼人杀死
`);

    this.prompts.set('role_witch', `
你的角色：女巫
目标：合理使用药品帮助好人获胜
策略要点：
1. 解药：救活被杀的玩家（通常是重要角色）
2. 毒药：毒死确认的狼人
3. 不能自救
4. 药品使用要谨慎，时机很重要
5. 隐藏身份避免被针对
`);

    // 阶段特定提示词
    this.prompts.set('phase_day', `
现在是白天讨论阶段。你需要：
1. 分析昨晚发生的事情
2. 表达你的怀疑和推理
3. 与其他玩家互动讨论
4. 为即将到来的投票做准备
`);

    this.prompts.set('phase_vote', `
现在是投票阶段。你需要：
1. 根据讨论结果选择投票目标
2. 简要说明投票理由
3. 考虑你的角色目标
4. 注意投票可能的后果
`);

    this.prompts.set('phase_night', `
现在是夜晚阶段。根据你的角色：
- 狼人：选择杀人目标
- 预言家：选择查验目标
- 女巫：决定是否使用药品
- 守卫：选择守护目标
- 其他角色：等待夜晚结束
`);
  }

  // 为玩家分配性格
  assignPersonality(player: Player): RolePersonality | null {
    if (!player.role) return null;

    const personalities = this.personalities.get(player.role);
    if (!personalities || personalities.length === 0) {
      return null;
    }

    // 随机选择一个性格
    const randomIndex = Math.floor(Math.random() * personalities.length);
    return personalities[randomIndex];
  }

  // 生成角色扮演消息
  generateRolePlayingMessages(
    context: GameContext,
    personality: RolePersonality,
    action: 'speak' | 'vote' | 'skill'
  ): LLMMessage[] {
    const messages: LLMMessage[] = [];

    // 系统提示词
    messages.push({
      role: 'system',
      content: this.buildSystemPrompt(context, personality)
    });

    // 游戏状态信息
    messages.push({
      role: 'user',
      content: this.buildGameStatePrompt(context)
    });

    // 行动特定提示
    messages.push({
      role: 'user',
      content: this.buildActionPrompt(context, action)
    });

    return messages;
  }

  // 构建系统提示词
  private buildSystemPrompt(context: GameContext, personality: RolePersonality): string {
    const basePrompt = this.prompts.get('system_base') || '';
    const rolePrompt = this.prompts.get(`role_${context.player.role}`) || '';
    
    return `${basePrompt}

你的角色信息：
- 角色：${ROLE_INFO[context.player.role!]?.name || context.player.role}
- 性格：${personality.name}
- 特征：${personality.traits.join('、')}
- 说话风格：${personality.speakingStyle}
- 策略：${personality.strategy}

${rolePrompt}

性格参数：
- 怀疑程度：${personality.suspicionLevel}/10
- 攻击性：${personality.aggressiveness}/10
- 逻辑性：${personality.logicalness}/10

请严格按照这个角色和性格来行动和发言。`;
  }

  // 构建游戏状态提示词
  private buildGameStatePrompt(context: GameContext): string {
    const { gameState, player, phase, round } = context;
    
    let prompt = `当前游戏状态：
- 游戏阶段：${this.getPhaseDescription(phase)}
- 回合数：第${round}轮
- 你的编号：${player.playerNumber}号
- 存活状态：${player.isAlive ? '存活' : '已死亡'}
- 是否警长：${player.isCaptain ? '是' : '否'}

存活玩家：`;

    gameState.players.filter(p => p.isAlive).forEach(p => {
      prompt += `\n- ${p.playerNumber}号${p.isCaptain ? '(警长)' : ''}`;
    });

    if (context.recentEvents.length > 0) {
      prompt += `\n\n最近发生的事件：\n${context.recentEvents.join('\n')}`;
    }

    return prompt;
  }

  // 构建行动提示词
  private buildActionPrompt(context: GameContext, action: 'speak' | 'vote' | 'skill'): string {
    const phasePrompt = this.prompts.get(`phase_${context.phase}`) || '';
    
    let actionPrompt = phasePrompt;

    switch (action) {
      case 'speak':
        actionPrompt += `\n\n请根据当前局势发言。你的发言应该：
1. 体现你的角色目标
2. 符合你的性格特征
3. 对局势有所推进
4. 长度控制在50-200字`;
        break;

      case 'vote':
        actionPrompt += `\n\n请选择你要投票的玩家编号，并简要说明理由。
格式：投票给X号，理由是...`;
        break;

      case 'skill':
        actionPrompt += `\n\n请根据你的角色能力选择行动：
- 如果你是狼人，选择要杀死的玩家
- 如果你是预言家，选择要查验的玩家
- 如果你是女巫，选择是否使用药品
- 如果你是守卫，选择要守护的玩家`;
        break;
    }

    return actionPrompt;
  }

  // 获取阶段描述
  private getPhaseDescription(phase: GamePhase): string {
    const descriptions = {
      preparation: '准备阶段',
      night: '夜晚阶段',
      day: '白天讨论阶段',
      vote: '投票阶段',
      finished: '游戏结束'
    };
    return descriptions[phase] || phase;
  }

  // 更新玩家记忆
  updatePlayerMemory(
    playerId: string, 
    key: string, 
    value: any,
    context: GameContext
  ): void {
    if (!context.playerMemory[playerId]) {
      context.playerMemory[playerId] = {};
    }
    context.playerMemory[playerId][key] = value;
  }

  // 获取玩家记忆
  getPlayerMemory(playerId: string, key: string, context: GameContext): any {
    return context.playerMemory[playerId]?.[key];
  }
}
