import { Router } from 'express';
import authRoutes from './auth';
import gameRoutes from './games';
import modelRoutes from './models';
import userRoutes from './users';
import statsRoutes from './stats';

const router = Router();

// API版本信息
router.get('/', (req, res) => {
  res.json({
    name: 'LLM狼人杀游戏API',
    version: '1.0.0',
    description: '展示不同LLM模型玩狼人杀游戏的后端API',
    endpoints: {
      auth: '/api/auth',
      games: '/api/games',
      models: '/api/models',
      users: '/api/users',
      stats: '/api/stats',
      health: '/api/health'
    },
    websocket: {
      url: 'ws://localhost:8000',
      events: [
        'game:join',
        'game:state_update',
        'player:speak',
        'player:vote',
        'game:phase_change',
        'game:end'
      ]
    }
  });
});

// 挂载路由
router.use('/auth', authRoutes);
router.use('/games', gameRoutes);
router.use('/models', modelRoutes);
router.use('/users', userRoutes);
router.use('/stats', statsRoutes);

export default router;
