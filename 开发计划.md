# LLM狼人杀游戏网站开发计划

## 项目概述

开发一个展示不同LLM模型模拟真人玩网易狼人杀游戏的网站，支持12人游戏模式，用户可以提供不同模型的API KEY来观看AI之间的对战。

## 技术架构

### 前端技术栈
- **框架**: Next.js 14 (React 18)
- **语言**: TypeScript
- **样式**: Tailwind CSS + Shadcn/ui
- **状态管理**: Zustand
- **实时通信**: Socket.io Client
- **构建工具**: Vite/Turbopack

### 后端技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js
- **语言**: TypeScript
- **数据库**: PostgreSQL (Supabase)
- **实时通信**: Socket.io
- **认证**: Supabase Auth

### DevOps & 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **环境管理**: Docker环境变量
- **开发环境**: 本地开发 + 服务器测试

### LLM集成
- **支持模型**:
  - OpenAI GPT-3.5/4
  - Anthropic Claude
  - Google Gemini
  - 国产模型（通义千问、文心一言等）
- **统一接口**: 抽象化API调用层
- **负载均衡**: 智能模型选择和错误处理

## 项目结构

```
werewolf-ai/
├── docker-compose.yml          # Docker编排文件
├── nginx.conf                  # Nginx配置
├── frontend/                   # Next.js前端
│   ├── Dockerfile
│   ├── package.json
│   ├── next.config.js
│   ├── src/
│   │   ├── app/               # App Router
│   │   ├── components/        # React组件
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── store/            # Zustand状态管理
│   │   ├── types/            # TypeScript类型
│   │   └── utils/            # 工具函数
│   └── public/               # 静态资源
├── backend/                    # Node.js后端
│   ├── Dockerfile
│   ├── package.json
│   ├── src/
│   │   ├── controllers/      # 控制器
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   │   ├── game/         # 游戏引擎
│   │   │   ├── ai/           # AI代理
│   │   │   └── llm/          # LLM集成
│   │   ├── routes/           # 路由
│   │   ├── middleware/       # 中间件
│   │   ├── utils/            # 工具函数
│   │   └── types/            # TypeScript类型
│   └── tests/                # 测试文件
├── shared/                     # 共享代码
│   ├── types/                # 共享类型定义
│   ├── constants/            # 常量
│   └── utils/                # 共享工具
└── docs/                      # 文档
    ├── api.md                # API文档
    ├── deployment.md         # 部署文档
    └── development.md        # 开发文档
```

## 核心功能模块

### 1. 游戏引擎 (Game Engine)
- **角色系统**: 狼人、预言家、女巫、猎人、白痴、守卫、平民
- **状态机**: 夜晚/白天阶段切换
- **规则引擎**: 技能使用、投票、胜负判定
- **事件系统**: 游戏事件的发布和订阅

### 2. AI代理系统 (AI Agent)
- **角色扮演**: 不同角色的行为模式和发言风格
- **决策引擎**: 基于游戏状态的智能决策
- **记忆系统**: 游戏过程中的信息记录和分析
- **策略模式**: 不同难度和风格的AI策略

### 3. LLM集成层 (LLM Integration)
- **统一接口**: 抽象化不同模型的API调用
- **Prompt管理**: 角色扮演和决策的Prompt模板
- **错误处理**: API调用失败的重试和降级机制
- **性能监控**: 响应时间、成功率统计

### 4. 实时通信 (Real-time Communication)
- **WebSocket**: 游戏状态实时同步
- **房间管理**: 游戏房间的创建和管理
- **事件广播**: 游戏事件的实时推送
- **连接管理**: 断线重连和状态恢复

### 5. 前端界面 (Frontend UI)
- **游戏大厅**: 房间列表、创建房间、模型选择
- **游戏界面**: 玩家列表、聊天区、投票区、技能面板
- **实时展示**: AI对话的实时显示和动画
- **回放系统**: 游戏历史记录和回放功能
- **管理后台**: API Key管理、模型配置、数据统计

## 开发阶段规划

### 阶段1: 项目初始化 (1-2天)
- [x] 技术栈选择和环境搭建
- [x] Docker环境配置
- [x] 项目结构创建
- [x] 基础依赖安装
- [x] 数据库设计
- [x] API接口设计

#### 已完成工作详情:
**API接口设计:**
- ✅ 创建了完整的RESTful API文档(docs/api.md)
- ✅ 实现了基础路由结构(认证、游戏、模型、用户、统计)
- ✅ 添加了输入验证和错误处理
- ✅ 设计了WebSocket事件规范
- ✅ 集成了Express路由到主应用

**Docker环境配置:**
- ✅ 创建了开发环境和生产环境的docker-compose.yml
- ✅ 配置了前后端的Dockerfile和生产环境Dockerfile
- ✅ 设置了Nginx反向代理配置
- ✅ 创建了环境变量模板(.env.example)

**项目结构创建:**
- ✅ 后端: Express + TypeScript + Socket.io架构
- ✅ 前端: Next.js 14 + React 18 + Tailwind CSS
- ✅ 数据库: PostgreSQL + Redis
- ✅ 完整的目录结构和配置文件

**基础依赖安装:**
- ✅ 后端package.json: Express, Socket.io, Prisma, LLM SDKs
- ✅ 前端package.json: Next.js, React, Tailwind, Zustand
- ✅ TypeScript配置和ESLint配置

**数据库设计:**
- ✅ 用户表、游戏表、游戏玩家表
- ✅ 游戏日志表、AI模型配置表
- ✅ 用户API密钥表
- ✅ 数据库初始化脚本(init.sql)

### 阶段2: 游戏引擎开发 (3-5天)
- [x] 狼人杀游戏规则实现
- [x] 角色系统开发
- [x] 游戏状态管理
- [x] 胜负判定系统
- [ ] 单元测试编写

#### 已完成工作详情:
**游戏引擎开发:**
- ✅ 创建了GameEngine核心引擎类
- ✅ 实现了完整游戏流程（准备→夜晚→白天→投票→结束）
- ✅ 添加了角色分配和洗牌算法
- ✅ 实现了完整的胜负判定逻辑
- ✅ 创建了GameManager游戏管理器
- ✅ 集成了数据库存储和缓存机制

**角色系统开发:**
- ✅ 创建了RoleSystem角色系统类
- ✅ 实现了所有基础角色技能（狼人、预言家、女巫、猎人、守卫、白痴、狼王）
- ✅ 添加了夜晚行动处理和效果解决机制
- ✅ 实现了技能冲突处理（守卫vs杀人、女巫救人等）

**游戏状态管理:**
- ✅ 实现了完整的游戏状态机
- ✅ 添加了阶段计时器和自动切换
- ✅ 创建了WebSocket实时通信处理器
- ✅ 集成了事件驱动的状态同步

### 阶段3: LLM集成和AI代理 (4-6天)
- [x] 多模型API统一接口
- [x] AI角色扮演系统
- [x] AI决策引擎
- [ ] Prompt工程和优化
- [ ] 模型性能监控

#### 已完成工作详情:
**多模型API统一接口:**
- ✅ 创建了LLMProvider抽象基类
- ✅ 实现了OpenAI和Claude提供商
- ✅ 添加了统一的错误处理和重试机制
- ✅ 创建了LLMManager统一管理器
- ✅ 集成了缓存和性能监控

**AI角色扮演系统:**
- ✅ 设计了多种角色性格（冷静狼人、激进狼人、理性村民等）
- ✅ 创建了角色特定的prompt模板
- ✅ 实现了性格特征和说话风格系统
- ✅ 添加了游戏上下文感知能力

**AI决策引擎:**
- ✅ 实现了基于LLM的智能决策系统
- ✅ 添加了玩家状态跟踪（怀疑度、信任度、记忆）
- ✅ 创建了多种决策类型（发言、投票、技能使用）
- ✅ 集成了角色扮演和游戏逻辑

### 阶段4: 前端界面开发 (4-6天)
- [x] 游戏大厅界面
- [x] 游戏主界面
- [x] 实时对话展示
- [x] 响应式设计
- [x] 用户体验优化

#### 已完成工作详情:
**React组件设计:**
- ✅ 创建了基础UI组件库（Button、Card等）
- ✅ 使用Tailwind CSS + 自定义样式
- ✅ 实现了响应式设计和暗色主题

**游戏大厅界面:**
- ✅ 游戏列表展示和筛选功能
- ✅ 创建游戏对话框
- ✅ 支持多种AI模型选择（包括硅基流动）
- ✅ 游戏状态实时更新

**游戏房间界面:**
- ✅ 12人玩家布局展示
- ✅ 游戏阶段和倒计时显示
- ✅ 实时游戏日志
- ✅ 角色信息展示（游戏结束后）

**实时通信:**
- ✅ WebSocket连接管理Hook
- ✅ 游戏状态实时同步
- ✅ 消息和事件处理
- ✅ 连接状态监控和错误处理

**硅基流动集成:**
- ✅ 添加SiliconFlowProvider实现
- ✅ 支持Qwen、GLM-4、Llama等模型
- ✅ 流式响应和错误处理
- ✅ 更新前后端配置和数据库

### 阶段5: 实时通信功能 (2-3天)
- [x] WebSocket服务搭建
- [x] 房间管理系统
- [x] 实时状态同步
- [x] 断线重连机制

### 阶段6: 高级功能 (3-4天)
- [ ] 游戏回放功能
- [ ] 管理后台界面
- [ ] 数据统计和分析
- [ ] 性能优化

### 阶段7: 测试和部署 (2-3天)
- [ ] 功能测试
- [ ] 性能测试
- [ ] Docker部署配置
- [ ] 服务器部署
- [ ] 监控和日志

## Docker部署方案

### 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    environment:
      - NODE_ENV=development
  
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://...
  
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=werewolf
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### 生产环境
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
  
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
  
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
```

## API设计概览

### RESTful API
- `GET /api/games` - 获取游戏列表
- `POST /api/games` - 创建游戏
- `GET /api/games/:id` - 获取游戏详情
- `POST /api/games/:id/join` - 加入游戏
- `GET /api/models` - 获取可用模型列表
- `POST /api/models/config` - 配置模型API Key

### WebSocket Events
- `game:created` - 游戏创建
- `game:joined` - 玩家加入
- `game:started` - 游戏开始
- `game:phase_changed` - 阶段切换
- `player:speak` - 玩家发言
- `player:vote` - 玩家投票
- `game:ended` - 游戏结束

## 当前进展总结

### 🎉 已完成的主要功能

1. **完整的项目架构** - Docker化的前后端分离架构
2. **游戏引擎系统** - 完整的狼人杀游戏逻辑和规则引擎
3. **角色系统** - 12种角色的技能和交互机制
4. **LLM集成** - 支持OpenAI、Claude等多种模型的统一接口
5. **AI代理系统** - 智能的角色扮演和决策引擎
6. **实时通信** - WebSocket游戏状态同步
7. **数据库设计** - 完整的数据模型和缓存机制

### 🚀 可以开始测试

项目的核心功能已经完成，现在可以：

1. **启动开发环境**: `docker-compose up -d`
2. **运行后端服务**: `cd backend && npm run dev`
3. **测试游戏系统**: `npm run test:game`
4. **配置API密钥**: 在环境变量中添加LLM API密钥

### 📋 下一步计划

1. **前端界面开发** - 创建游戏大厅和游戏界面
2. **Prompt优化** - 改进AI角色扮演效果
3. **性能监控** - 添加详细的监控和日志
4. **测试和调试** - 完善游戏逻辑和AI行为
