// 游戏相关类型定义

export type GameStatus = 'waiting' | 'playing' | 'finished';
export type GameMode = 'standard_12' | 'advanced_12' | 'custom';
export type PlayerType = 'ai' | 'human';
export type WinnerTeam = 'werewolf' | 'villager' | 'third_party';

// 角色类型
export type Role = 
  | 'werewolf'      // 狼人
  | 'villager'      // 村民
  | 'prophet'       // 预言家
  | 'witch'         // 女巫
  | 'hunter'        // 猎人
  | 'idiot'         // 白痴
  | 'guard'         // 守卫
  | 'wolf_king'     // 狼王
  | 'white_wolf_king' // 白狼王
  | 'dream_catcher' // 摄梦人
  | 'cupid'         // 丘比特
  | 'thief';        // 盗贼

// 游戏阶段
export type GamePhase = 
  | 'preparation'   // 准备阶段
  | 'night'         // 夜晚
  | 'day'           // 白天
  | 'vote'          // 投票
  | 'finished';     // 结束

// 行动类型
export type ActionType = 
  | 'speak'         // 发言
  | 'vote'          // 投票
  | 'skill_use'     // 技能使用
  | 'kill'          // 杀人
  | 'check'         // 查验
  | 'save'          // 救人
  | 'poison'        // 毒杀
  | 'guard'         // 守护
  | 'shoot'         // 开枪
  | 'explode'       // 自爆
  | 'dream'         // 摄梦
  | 'couple';       // 连情侣

// AI模型提供商
export type AIProvider = 'openai' | 'anthropic' | 'google' | 'qwen' | 'ernie' | 'spark';

// 游戏配置
export interface GameConfig {
  mode: GameMode;
  maxPlayers: number;
  roles: Role[];
  timeSettings: {
    dayPhase: number;      // 白天发言时间(秒)
    votePhase: number;     // 投票时间(秒)
    nightPhase: number;    // 夜晚技能时间(秒)
    lastWords: number;     // 遗言时间(秒)
  };
  rules: {
    hasPolice: boolean;    // 是否有警长
    firstNightLastWords: boolean; // 首夜是否有遗言
    darkGame: boolean;     // 是否暗牌局
  };
}

// 玩家信息
export interface Player {
  id: string;
  playerNumber: number;   // 1-12
  playerType: PlayerType;
  aiModel?: string;
  role?: Role;
  isAlive: boolean;
  isCaptain: boolean;     // 是否是警长
  isCouple?: boolean;     // 是否是情侣
  coupleWith?: number;    // 情侣对象
  votes: number;          // 得票数
  hasVoted: boolean;      // 是否已投票
  votedFor?: number;      // 投票给谁
}

// 游戏状态
export interface GameState {
  id: string;
  name: string;
  status: GameStatus;
  mode: GameMode;
  config: GameConfig;
  players: Player[];
  currentPhase: GamePhase;
  currentRound: number;
  currentSpeaker?: number;
  timeRemaining: number;
  winner?: WinnerTeam;
  createdAt: string;
  startedAt?: string;
  finishedAt?: string;
}

// 游戏行动
export interface GameAction {
  id: string;
  gameId: string;
  phase: GamePhase;
  round: number;
  actionType: ActionType;
  playerNumber?: number;
  targetPlayer?: number;
  content?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

// AI模型配置
export interface AIModel {
  id: string;
  name: string;
  displayName: string;
  provider: AIProvider;
  modelId: string;
  apiEndpoint?: string;
  isActive: boolean;
  defaultConfig: {
    temperature: number;
    maxTokens: number;
    [key: string]: any;
  };
}

// 用户API密钥
export interface UserAPIKey {
  id: string;
  userId: string;
  provider: AIProvider;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// WebSocket事件类型
export interface SocketEvents {
  // 游戏事件
  'game:created': (game: GameState) => void;
  'game:joined': (player: Player) => void;
  'game:started': (game: GameState) => void;
  'game:phase_changed': (phase: GamePhase, timeRemaining: number) => void;
  'game:ended': (winner: WinnerTeam, game: GameState) => void;
  
  // 玩家事件
  'player:speak': (action: GameAction) => void;
  'player:vote': (action: GameAction) => void;
  'player:skill_use': (action: GameAction) => void;
  'player:died': (playerNumber: number, reason: string) => void;
  
  // 系统事件
  'system:message': (message: string) => void;
  'system:error': (error: string) => void;
  
  // 连接事件
  'connect': () => void;
  'disconnect': () => void;
  'ping': () => void;
  'pong': (data: { timestamp: number }) => void;
}
