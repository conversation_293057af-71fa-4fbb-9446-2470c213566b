import { Player, GameState, GamePhase, Role } from '../../../shared/types/game';
import { LLMManager } from '../llm/LLMManager';
import { RolePlayingSystem, GameContext, RolePersonality } from './RolePlayingSystem';
import { LLMMessage } from '../llm/LLMProvider';

export interface DecisionResult {
  action: 'speak' | 'vote' | 'skill_use';
  content?: string;
  target?: number;
  skillName?: string;
  confidence: number; // 0-1, 决策置信度
  reasoning: string;
}

export interface AIPlayerState {
  player: Player;
  personality: RolePersonality;
  memory: { [key: string]: any };
  suspicions: { [playerNumber: number]: number }; // 对其他玩家的怀疑度 0-10
  trust: { [playerNumber: number]: number }; // 对其他玩家的信任度 0-10
  lastActions: string[];
}

export class DecisionEngine {
  private llmManager: LLMManager;
  private rolePlayingSystem: RolePlayingSystem;
  private playerStates: Map<string, AIPlayerState> = new Map();

  constructor() {
    this.llmManager = LLMManager.getInstance();
    this.rolePlayingSystem = new RolePlayingSystem();
  }

  // 初始化AI玩家
  initializeAIPlayer(player: Player): AIPlayerState {
    const personality = this.rolePlayingSystem.assignPersonality(player);
    if (!personality) {
      throw new Error(`无法为角色 ${player.role} 分配性格`);
    }

    const aiState: AIPlayerState = {
      player,
      personality,
      memory: {},
      suspicions: {},
      trust: {},
      lastActions: []
    };

    // 初始化对其他玩家的基础信任度和怀疑度
    for (let i = 1; i <= 12; i++) {
      if (i !== player.playerNumber) {
        aiState.suspicions[i] = 5; // 中性怀疑度
        aiState.trust[i] = 5; // 中性信任度
      }
    }

    this.playerStates.set(player.id, aiState);
    return aiState;
  }

  // 获取AI玩家状态
  getAIPlayerState(playerId: string): AIPlayerState | null {
    return this.playerStates.get(playerId) || null;
  }

  // 做出决策
  async makeDecision(
    playerId: string,
    gameState: GameState,
    phase: GamePhase,
    actionType: 'speak' | 'vote' | 'skill_use'
  ): Promise<DecisionResult> {
    const aiState = this.playerStates.get(playerId);
    if (!aiState) {
      throw new Error(`AI玩家状态未找到: ${playerId}`);
    }

    // 构建游戏上下文
    const context: GameContext = {
      gameState,
      player: aiState.player,
      phase,
      round: gameState.currentRound,
      recentEvents: this.getRecentEvents(gameState),
      playerMemory: { [playerId]: aiState.memory }
    };

    // 生成角色扮演消息
    const messages = this.rolePlayingSystem.generateRolePlayingMessages(
      context,
      aiState.personality,
      actionType
    );

    // 添加决策上下文
    messages.push({
      role: 'user',
      content: this.buildDecisionContext(aiState, gameState, actionType)
    });

    try {
      // 调用LLM获取决策
      const response = await this.llmManager.sendMessage(
        messages,
        aiState.player.aiModel?.split('-')[0] as any, // 提取提供商名称
        aiState.player.aiModel,
        {
          temperature: 0.7,
          maxTokens: 300
        }
      );

      // 解析决策结果
      const decision = this.parseDecisionResponse(response.content, actionType);
      
      // 更新AI状态
      this.updateAIState(aiState, decision, gameState);

      return decision;
    } catch (error) {
      console.error(`AI决策失败 (${playerId}):`, error);
      
      // 返回默认决策
      return this.getDefaultDecision(aiState, actionType, gameState);
    }
  }

  // 构建决策上下文
  private buildDecisionContext(
    aiState: AIPlayerState,
    gameState: GameState,
    actionType: 'speak' | 'vote' | 'skill_use'
  ): string {
    let context = `决策上下文：\n`;

    // 添加怀疑度信息
    context += `你对其他玩家的怀疑度：\n`;
    Object.entries(aiState.suspicions).forEach(([playerNum, suspicion]) => {
      const player = gameState.players.find(p => p.playerNumber === parseInt(playerNum));
      if (player?.isAlive) {
        context += `- ${playerNum}号: ${suspicion}/10\n`;
      }
    });

    // 添加信任度信息
    context += `\n你对其他玩家的信任度：\n`;
    Object.entries(aiState.trust).forEach(([playerNum, trust]) => {
      const player = gameState.players.find(p => p.playerNumber === parseInt(playerNum));
      if (player?.isAlive) {
        context += `- ${playerNum}号: ${trust}/10\n`;
      }
    });

    // 根据行动类型添加特定指导
    switch (actionType) {
      case 'speak':
        context += `\n请发言表达你的观点。要求：
1. 体现你的性格特征
2. 推进游戏进程
3. 符合你的角色目标
4. 自然真实，不要暴露你是AI`;
        break;

      case 'vote':
        context += `\n请选择投票目标。考虑因素：
1. 你的怀疑度评估
2. 你的角色目标
3. 当前局势分析
4. 其他玩家的发言

请回复格式：投票：X号，理由：...`;
        break;

      case 'skill_use':
        context += this.getSkillUseContext(aiState.player.role!, gameState);
        break;
    }

    return context;
  }

  // 获取技能使用上下文
  private getSkillUseContext(role: Role, gameState: GameState): string {
    switch (role) {
      case 'werewolf':
      case 'wolf_king':
        return `\n作为狼人，请选择今晚要杀死的目标：
1. 优先考虑神职角色
2. 避免杀死队友
3. 考虑战略价值

请回复格式：杀死：X号，理由：...`;

      case 'prophet':
        return `\n作为预言家，请选择今晚要查验的玩家：
1. 选择最可疑的玩家
2. 或验证重要玩家身份
3. 避免浪费查验机会

请回复格式：查验：X号，理由：...`;

      case 'witch':
        return `\n作为女巫，请选择是否使用药品：
1. 解药：救活被杀的重要角色
2. 毒药：毒死确认的狼人
3. 不使用：保留药品

请回复格式：使用解药/使用毒药对X号/不使用，理由：...`;

      case 'guard':
        return `\n作为守卫，请选择今晚要守护的玩家：
1. 保护重要的神职角色
2. 不能连续守护同一人
3. 考虑狼人可能的目标

请回复格式：守护：X号，理由：...`;

      default:
        return `\n你的角色在夜晚没有特殊技能，请等待。`;
    }
  }

  // 解析决策响应
  private parseDecisionResponse(content: string, actionType: 'speak' | 'vote' | 'skill_use'): DecisionResult {
    const decision: DecisionResult = {
      action: actionType,
      confidence: 0.7,
      reasoning: content
    };

    try {
      switch (actionType) {
        case 'speak':
          decision.content = content.trim();
          break;

        case 'vote':
          const voteMatch = content.match(/投票[：:]\s*(\d+)号/);
          if (voteMatch) {
            decision.target = parseInt(voteMatch[1]);
          }
          decision.content = content;
          break;

        case 'skill_use':
          // 解析技能使用
          const killMatch = content.match(/杀死[：:]\s*(\d+)号/);
          const checkMatch = content.match(/查验[：:]\s*(\d+)号/);
          const saveMatch = content.match(/使用解药/);
          const poisonMatch = content.match(/使用毒药.*?(\d+)号/);
          const guardMatch = content.match(/守护[：:]\s*(\d+)号/);

          if (killMatch) {
            decision.target = parseInt(killMatch[1]);
            decision.skillName = 'kill';
          } else if (checkMatch) {
            decision.target = parseInt(checkMatch[1]);
            decision.skillName = 'check';
          } else if (saveMatch) {
            decision.skillName = 'save';
          } else if (poisonMatch) {
            decision.target = parseInt(poisonMatch[1]);
            decision.skillName = 'poison';
          } else if (guardMatch) {
            decision.target = parseInt(guardMatch[1]);
            decision.skillName = 'guard';
          }
          break;
      }
    } catch (error) {
      console.error('解析决策响应失败:', error);
    }

    return decision;
  }

  // 更新AI状态
  private updateAIState(aiState: AIPlayerState, decision: DecisionResult, gameState: GameState): void {
    // 记录行动
    aiState.lastActions.push(`${decision.action}: ${decision.content || decision.target}`);
    if (aiState.lastActions.length > 10) {
      aiState.lastActions.shift(); // 保持最近10个行动
    }

    // 根据决策更新怀疑度和信任度
    if (decision.action === 'vote' && decision.target) {
      // 投票会增加对目标的怀疑度
      aiState.suspicions[decision.target] = Math.min(10, aiState.suspicions[decision.target] + 1);
    }

    // 更新记忆
    aiState.memory.lastDecision = decision;
    aiState.memory.lastGameState = gameState;
  }

  // 获取默认决策
  private getDefaultDecision(
    aiState: AIPlayerState,
    actionType: 'speak' | 'vote' | 'skill_use',
    gameState: GameState
  ): DecisionResult {
    switch (actionType) {
      case 'speak':
        return {
          action: 'speak',
          content: '我需要再观察一下局势。',
          confidence: 0.3,
          reasoning: '默认发言'
        };

      case 'vote':
        // 投票给怀疑度最高的玩家
        const mostSuspicious = this.getMostSuspiciousPlayer(aiState, gameState);
        return {
          action: 'vote',
          target: mostSuspicious,
          content: `投票给${mostSuspicious}号，感觉比较可疑。`,
          confidence: 0.3,
          reasoning: '默认投票'
        };

      case 'skill_use':
        return this.getDefaultSkillDecision(aiState, gameState);

      default:
        return {
          action: actionType,
          confidence: 0.1,
          reasoning: '未知行动类型'
        };
    }
  }

  // 获取最可疑的玩家
  private getMostSuspiciousPlayer(aiState: AIPlayerState, gameState: GameState): number {
    let maxSuspicion = 0;
    let mostSuspicious = 1;

    Object.entries(aiState.suspicions).forEach(([playerNum, suspicion]) => {
      const playerNumber = parseInt(playerNum);
      const player = gameState.players.find(p => p.playerNumber === playerNumber);
      
      if (player?.isAlive && playerNumber !== aiState.player.playerNumber && suspicion > maxSuspicion) {
        maxSuspicion = suspicion;
        mostSuspicious = playerNumber;
      }
    });

    return mostSuspicious;
  }

  // 获取默认技能决策
  private getDefaultSkillDecision(aiState: AIPlayerState, gameState: GameState): DecisionResult {
    const role = aiState.player.role!;
    
    switch (role) {
      case 'werewolf':
      case 'wolf_king':
        // 随机选择一个非狼人目标
        const targets = gameState.players.filter(p => 
          p.isAlive && 
          p.playerNumber !== aiState.player.playerNumber &&
          p.role !== 'werewolf' && 
          p.role !== 'wolf_king'
        );
        const randomTarget = targets[Math.floor(Math.random() * targets.length)];
        
        return {
          action: 'skill_use',
          target: randomTarget?.playerNumber || 1,
          skillName: 'kill',
          confidence: 0.3,
          reasoning: '默认杀人'
        };

      default:
        return {
          action: 'skill_use',
          confidence: 0.1,
          reasoning: '默认技能使用'
        };
    }
  }

  // 获取最近事件
  private getRecentEvents(gameState: GameState): string[] {
    // 这里应该从游戏日志中获取最近的事件
    // 简化实现，返回空数组
    return [];
  }

  // 清理资源
  cleanup(): void {
    this.playerStates.clear();
  }
}
