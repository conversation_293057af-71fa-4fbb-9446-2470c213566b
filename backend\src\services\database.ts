import { PrismaClient } from '@prisma/client';
import { createClient } from 'redis';

// Prisma客户端
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
});

// Redis客户端
export const redis = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
});

// 数据库连接初始化
export async function initializeDatabase() {
  try {
    // 连接Prisma
    await prisma.$connect();
    console.log('✅ Prisma数据库连接成功');

    // 连接Redis
    await redis.connect();
    console.log('✅ Redis连接成功');

    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ 数据库连接测试通过');

    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 数据库断开连接
export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
    await redis.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 数据库断开连接失败:', error);
  }
}

// 数据库健康检查
export async function checkDatabaseHealth() {
  try {
    // 检查Prisma连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 检查Redis连接
    await redis.ping();
    
    return {
      status: 'healthy',
      prisma: 'connected',
      redis: 'connected',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

// 缓存工具函数
export const cache = {
  // 设置缓存
  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await redis.setEx(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  },

  // 获取缓存
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  },

  // 删除缓存
  async del(key: string): Promise<void> {
    try {
      await redis.del(key);
    } catch (error) {
      console.error('Redis del error:', error);
    }
  },

  // 检查缓存是否存在
  async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis exists error:', error);
      return false;
    }
  },

  // 设置过期时间
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await redis.expire(key, ttl);
    } catch (error) {
      console.error('Redis expire error:', error);
    }
  }
};

// 游戏缓存键生成器
export const cacheKeys = {
  game: (gameId: string) => `game:${gameId}`,
  gameState: (gameId: string) => `game:state:${gameId}`,
  gamePlayer: (gameId: string, playerNumber: number) => `game:${gameId}:player:${playerNumber}`,
  userGames: (userId: string) => `user:${userId}:games`,
  aiModel: (modelName: string) => `ai:model:${modelName}`,
  userApiKey: (userId: string, provider: string) => `user:${userId}:apikey:${provider}`
};

// 数据库事务工具
export async function withTransaction<T>(
  callback: (tx: typeof prisma) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(callback);
}

// 批量操作工具
export const batch = {
  // 批量创建游戏日志
  async createGameLogs(logs: Array<{
    gameId: string;
    phase: string;
    roundNumber: number;
    actionType: string;
    playerNumber?: number;
    targetPlayer?: number;
    content?: string;
    metadata?: any;
  }>) {
    return await prisma.gameLog.createMany({
      data: logs
    });
  },

  // 批量更新玩家状态
  async updatePlayers(updates: Array<{
    id: string;
    data: {
      isAlive?: boolean;
      isCaptain?: boolean;
      role?: string;
    };
  }>) {
    const promises = updates.map(update =>
      prisma.gamePlayer.update({
        where: { id: update.id },
        data: update.data
      })
    );
    return await Promise.all(promises);
  }
};

// 清理过期数据
export async function cleanupExpiredData() {
  try {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    // 清理已结束超过24小时的游戏日志
    const deletedLogs = await prisma.gameLog.deleteMany({
      where: {
        game: {
          status: 'finished',
          finishedAt: {
            lt: oneDayAgo
          }
        }
      }
    });

    console.log(`🧹 清理了 ${deletedLogs.count} 条过期游戏日志`);
    
    return deletedLogs.count;
  } catch (error) {
    console.error('清理过期数据失败:', error);
    return 0;
  }
}

// 数据库统计信息
export async function getDatabaseStats() {
  try {
    const [
      totalUsers,
      totalGames,
      activeGames,
      totalLogs
    ] = await Promise.all([
      prisma.user.count(),
      prisma.game.count(),
      prisma.game.count({ where: { status: 'playing' } }),
      prisma.gameLog.count()
    ]);

    return {
      users: totalUsers,
      games: {
        total: totalGames,
        active: activeGames,
        finished: totalGames - activeGames
      },
      logs: totalLogs,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('获取数据库统计信息失败:', error);
    return null;
  }
}
