import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseSocketOptions {
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

interface SocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
}

export const useSocket = (
  url: string = process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:8000',
  options: UseSocketOptions = {}
) => {
  const socketRef = useRef<Socket | null>(null);
  const [socketState, setSocketState] = useState<SocketState>({
    connected: false,
    connecting: false,
    error: null
  });

  const {
    autoConnect = true,
    reconnection = true,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000
  } = options;

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [url, autoConnect]);

  const connect = () => {
    if (socketRef.current?.connected) {
      return;
    }

    setSocketState(prev => ({ ...prev, connecting: true, error: null }));

    socketRef.current = io(url, {
      reconnection,
      reconnectionAttempts,
      reconnectionDelay,
      timeout: 10000,
    });

    socketRef.current.on('connect', () => {
      console.log('🔌 WebSocket连接成功');
      setSocketState({
        connected: true,
        connecting: false,
        error: null
      });
    });

    socketRef.current.on('disconnect', (reason) => {
      console.log('🔌 WebSocket断开连接:', reason);
      setSocketState(prev => ({
        ...prev,
        connected: false,
        connecting: false
      }));
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('🔌 WebSocket连接错误:', error);
      setSocketState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: error.message
      }));
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      console.log('🔌 WebSocket重连成功, 尝试次数:', attemptNumber);
    });

    socketRef.current.on('reconnect_error', (error) => {
      console.error('🔌 WebSocket重连失败:', error);
    });

    socketRef.current.on('reconnect_failed', () => {
      console.error('🔌 WebSocket重连失败，已达到最大尝试次数');
      setSocketState(prev => ({
        ...prev,
        error: '连接失败，请刷新页面重试'
      }));
    });
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocketState({
        connected: false,
        connecting: false,
        error: null
      });
    }
  };

  const emit = (event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('WebSocket未连接，无法发送事件:', event);
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event: string, callback?: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback);
    }
  };

  return {
    socket: socketRef.current,
    ...socketState,
    connect,
    disconnect,
    emit,
    on,
    off
  };
};

// 游戏专用的Socket Hook
export const useGameSocket = (gameId?: string) => {
  const socket = useSocket();
  const [gameState, setGameState] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);

  useEffect(() => {
    if (!socket.connected || !gameId) return;

    // 加入游戏房间
    socket.emit('game:join', { gameId });

    // 监听游戏事件
    socket.on('game:joined', (data) => {
      console.log('加入游戏成功:', data);
      setGameState(data.gameState);
    });

    socket.on('game:state_update', (state) => {
      console.log('游戏状态更新:', state);
      setGameState(state);
    });

    socket.on('game:started', (state) => {
      console.log('游戏开始:', state);
      setGameState(state);
      addMessage({
        id: Date.now().toString(),
        type: 'system',
        content: '游戏开始！',
        timestamp: new Date().toISOString()
      });
    });

    socket.on('game:phase_change', (data) => {
      console.log('阶段变化:', data);
      addMessage({
        id: Date.now().toString(),
        type: 'system',
        content: `进入${data.phase}阶段，剩余时间：${data.timeRemaining}秒`,
        timestamp: new Date().toISOString()
      });
    });

    socket.on('player:speak', (data) => {
      console.log('玩家发言:', data);
      addMessage({
        id: Date.now().toString(),
        type: 'speak',
        playerNumber: data.playerNumber,
        content: data.content,
        timestamp: data.timestamp
      });
    });

    socket.on('player:vote', (data) => {
      console.log('玩家投票:', data);
      addMessage({
        id: Date.now().toString(),
        type: 'vote',
        playerNumber: data.playerNumber,
        content: `投票给${data.targetPlayer}号`,
        timestamp: data.timestamp
      });
    });

    socket.on('player:eliminated', (data) => {
      console.log('玩家淘汰:', data);
      addMessage({
        id: Date.now().toString(),
        type: 'system',
        content: `${data.playerNumber}号玩家被淘汰 (${data.reason})`,
        timestamp: new Date().toISOString()
      });
    });

    socket.on('game:ended', (data) => {
      console.log('游戏结束:', data);
      setGameState(data.gameState);
      addMessage({
        id: Date.now().toString(),
        type: 'system',
        content: `游戏结束！${data.winner === 'werewolf' ? '狼人' : '好人'}获胜！`,
        timestamp: new Date().toISOString()
      });
    });

    socket.on('error', (error) => {
      console.error('游戏错误:', error);
      addMessage({
        id: Date.now().toString(),
        type: 'system',
        content: `错误：${error.message}`,
        timestamp: new Date().toISOString()
      });
    });

    return () => {
      socket.off('game:joined');
      socket.off('game:state_update');
      socket.off('game:started');
      socket.off('game:phase_change');
      socket.off('player:speak');
      socket.off('player:vote');
      socket.off('player:eliminated');
      socket.off('game:ended');
      socket.off('error');
    };
  }, [socket.connected, gameId]);

  const addMessage = (message: any) => {
    setMessages(prev => [...prev, message].slice(-100)); // 保持最近100条消息
  };

  const joinGame = (gameId: string) => {
    socket.emit('game:join', { gameId });
  };

  const leaveGame = (gameId: string) => {
    socket.emit('game:leave', { gameId });
  };

  return {
    ...socket,
    gameState,
    messages,
    joinGame,
    leaveGame
  };
};
