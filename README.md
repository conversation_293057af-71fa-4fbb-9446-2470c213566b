# LLM狼人杀游戏网站

一个展示不同LLM模型模拟真人玩网易狼人杀游戏的网站，支持12人游戏模式。

## 项目特色

- 🤖 **多模型支持**: 集成OpenAI、<PERSON>、Gemini等多种LLM模型
- 🎮 **完整游戏**: 实现网易狼人杀12人标准场完整规则
- 🔄 **实时对战**: WebSocket实时通信，观看AI之间的精彩对决
- 🐳 **Docker部署**: 完整的容器化解决方案
- 📱 **响应式设计**: 支持桌面和移动端访问

## 技术栈

### 前端
- Next.js 14 + React 18
- TypeScript
- Tailwind CSS + Shadcn/ui
- Socket.io Client
- Zustand (状态管理)

### 后端
- Node.js + Express
- TypeScript
- Socket.io (WebSocket)
- PostgreSQL + Prisma
- Redis (缓存)

### 部署
- Docker + Docker Compose
- Nginx (反向代理)

## 快速开始

### 环境要求

- Node.js 18+
- Docker & Docker Compose
- Git

### 1. 克隆项目

```bash
git clone <repository-url>
cd werewolf-ai
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量，添加你的API Keys
nano .env
```

### 3. 启动开发环境

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 4. 访问应用

- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- 数据库: localhost:5432

## 项目结构

```
werewolf-ai/
├── docker-compose.yml          # Docker编排文件
├── docker-compose.prod.yml     # 生产环境配置
├── nginx/                      # Nginx配置
├── frontend/                   # Next.js前端
│   ├── src/
│   │   ├── app/               # App Router
│   │   ├── components/        # React组件
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── store/            # Zustand状态
│   │   └── types/            # TypeScript类型
│   └── Dockerfile
├── backend/                    # Node.js后端
│   ├── src/
│   │   ├── controllers/      # 控制器
│   │   ├── services/         # 业务逻辑
│   │   │   ├── game/         # 游戏引擎
│   │   │   ├── ai/           # AI代理
│   │   │   └── llm/          # LLM集成
│   │   └── routes/           # 路由
│   └── Dockerfile
└── shared/                     # 共享代码
    ├── types/                 # 共享类型
    └── constants/             # 常量
```

## 游戏规则

本项目实现了网易狼人杀12人标准场规则：

### 角色配置
- **狼人阵营**: 4名狼人
- **好人阵营**: 8名好人
  - 神职: 预言家、女巫、猎人、白痴
  - 平民: 4名

### 游戏流程
1. **夜晚阶段**: 狼人杀人，神职使用技能
2. **白天阶段**: 讨论发言，投票放逐
3. **胜负判定**: 狼人屠边 vs 好人抓完狼

详细规则请查看 [游戏规则.md](./游戏规则.md)

## 开发指南

### 本地开发

```bash
# 启动开发环境
docker-compose up -d

# 进入后端容器
docker-compose exec backend sh

# 进入前端容器
docker-compose exec frontend sh

# 查看日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 数据库操作

```bash
# 运行数据库迁移
docker-compose exec backend npm run db:migrate

# 生成Prisma客户端
docker-compose exec backend npm run db:generate

# 数据库种子数据
docker-compose exec backend npm run db:seed
```

### 测试

```bash
# 后端测试
docker-compose exec backend npm test

# 前端测试
docker-compose exec frontend npm test
```

## 生产部署

### 1. 准备服务器

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境

```bash
# 复制生产环境配置
cp .env.example .env

# 编辑生产环境变量
nano .env
```

### 3. 部署应用

```bash
# 构建并启动生产环境
docker-compose -f docker-compose.prod.yml up -d --build

# 查看状态
docker-compose -f docker-compose.prod.yml ps
```

## API文档

### 游戏相关
- `GET /api/games` - 获取游戏列表
- `POST /api/games` - 创建游戏
- `POST /api/games/:id/join` - 加入游戏

### 模型配置
- `GET /api/models` - 获取可用模型
- `POST /api/models/config` - 配置API Key

### WebSocket事件
- `game:created` - 游戏创建
- `game:started` - 游戏开始
- `player:speak` - 玩家发言
- `game:ended` - 游戏结束

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
