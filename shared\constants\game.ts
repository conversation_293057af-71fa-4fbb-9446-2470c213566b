// 游戏常量定义

import { GameConfig, Role } from '../types/game';

// 默认游戏配置
export const DEFAULT_GAME_CONFIGS: Record<string, GameConfig> = {
  standard_12: {
    mode: 'standard_12',
    maxPlayers: 12,
    roles: [
      'werewolf', 'werewolf', 'werewolf', 'werewolf',  // 4个狼人
      'prophet', 'witch', 'hunter', 'idiot',           // 4个神职
      'villager', 'villager', 'villager', 'villager'   // 4个村民
    ],
    timeSettings: {
      dayPhase: 120,      // 2分钟发言
      votePhase: 15,      // 15秒投票
      nightPhase: 30,     // 30秒夜晚技能
      lastWords: 120      // 2分钟遗言
    },
    rules: {
      hasPolice: true,
      firstNightLastWords: true,
      darkGame: true
    }
  },
  advanced_12: {
    mode: 'advanced_12',
    maxPlayers: 12,
    roles: [
      'wolf_king', 'werewolf', 'werewolf', 'werewolf', // 1狼王+3狼人
      'prophet', 'witch', 'hunter', 'guard',           // 4个神职
      'villager', 'villager', 'villager', 'villager'   // 4个村民
    ],
    timeSettings: {
      dayPhase: 120,
      votePhase: 15,
      nightPhase: 30,
      lastWords: 120
    },
    rules: {
      hasPolice: true,
      firstNightLastWords: true,
      darkGame: true
    }
  }
};

// 角色信息
export const ROLE_INFO: Record<Role, {
  name: string;
  team: 'werewolf' | 'villager' | 'third_party';
  description: string;
  skills: string[];
}> = {
  werewolf: {
    name: '狼人',
    team: 'werewolf',
    description: '每天夜里可以杀死一个人',
    skills: ['夜晚杀人']
  },
  wolf_king: {
    name: '狼王',
    team: 'werewolf',
    description: '狼人阵营，具有死后开枪技能',
    skills: ['夜晚杀人', '死后开枪']
  },
  white_wolf_king: {
    name: '白狼王',
    team: 'werewolf',
    description: '可以在白天自爆时选择带走一名玩家',
    skills: ['夜晚杀人', '白天自爆带人']
  },
  villager: {
    name: '村民',
    team: 'villager',
    description: '没有任何能力，一觉睡到天亮',
    skills: []
  },
  prophet: {
    name: '预言家',
    team: 'villager',
    description: '每天晚上可以查看一名玩家的身份',
    skills: ['夜晚查验身份']
  },
  witch: {
    name: '女巫',
    team: 'villager',
    description: '拥有解药和毒药各一瓶',
    skills: ['救人', '毒杀']
  },
  hunter: {
    name: '猎人',
    team: 'villager',
    description: '被狼人杀害或被投票放逐时可以开枪',
    skills: ['死后开枪']
  },
  idiot: {
    name: '白痴',
    team: 'villager',
    description: '被投票出局时可以翻牌免疫',
    skills: ['投票免疫']
  },
  guard: {
    name: '守卫',
    team: 'villager',
    description: '每晚可以守护一名玩家',
    skills: ['夜晚守护']
  },
  dream_catcher: {
    name: '摄梦人',
    team: 'villager',
    description: '每晚可以选择一名玩家摄梦',
    skills: ['摄梦']
  },
  cupid: {
    name: '丘比特',
    team: 'third_party',
    description: '第一晚可以连接两名玩家为情侣',
    skills: ['连情侣']
  },
  thief: {
    name: '盗贼',
    team: 'third_party',
    description: '第一晚从两张身份牌中选择一张',
    skills: ['选择身份']
  }
};

// 胜利条件
export const WIN_CONDITIONS = {
  werewolf: {
    name: '狼人胜利',
    description: '屠边成功（杀死所有神职或所有平民）'
  },
  villager: {
    name: '好人胜利',
    description: '所有狼人死亡'
  },
  third_party: {
    name: '第三方胜利',
    description: '满足特殊胜利条件'
  }
};

// 游戏阶段时间配置
export const PHASE_DURATIONS = {
  preparation: 30,    // 准备阶段30秒
  night: 60,          // 夜晚阶段60秒
  day: 120,           // 白天发言120秒
  vote: 15,           // 投票阶段15秒
  finished: 0         // 结束阶段
};

// AI模型默认配置
export const AI_MODEL_CONFIGS = {
  openai: {
    temperature: 0.8,
    maxTokens: 1000,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0
  },
  anthropic: {
    temperature: 0.8,
    maxTokens: 1000,
    topP: 1
  },
  google: {
    temperature: 0.8,
    maxOutputTokens: 1000,
    topP: 1,
    topK: 40
  }
};

// 错误消息
export const ERROR_MESSAGES = {
  GAME_NOT_FOUND: '游戏不存在',
  GAME_FULL: '游戏人数已满',
  GAME_ALREADY_STARTED: '游戏已经开始',
  PLAYER_NOT_FOUND: '玩家不存在',
  INVALID_ACTION: '无效的行动',
  NOT_YOUR_TURN: '不是你的回合',
  SKILL_ALREADY_USED: '技能已经使用过',
  INVALID_TARGET: '无效的目标',
  API_KEY_REQUIRED: '需要配置API密钥',
  MODEL_NOT_AVAILABLE: '模型不可用'
};

// 系统消息模板
export const SYSTEM_MESSAGES = {
  GAME_CREATED: '游戏已创建',
  GAME_STARTED: '游戏开始！',
  PHASE_CHANGED: (phase: string) => `进入${phase}阶段`,
  PLAYER_JOINED: (playerNumber: number) => `玩家${playerNumber}号加入游戏`,
  PLAYER_DIED: (playerNumber: number, reason: string) => `玩家${playerNumber}号${reason}`,
  GAME_ENDED: (winner: string) => `游戏结束，${winner}获胜！`
};

// 夜晚行动顺序
export const NIGHT_ACTION_ORDER: Role[] = [
  'thief',          // 盗贼
  'cupid',          // 丘比特
  'werewolf',       // 狼人杀人
  'wolf_king',      // 狼王杀人
  'dream_catcher',  // 摄梦人
  'prophet',        // 预言家查验
  'witch',          // 女巫救人/毒人
  'guard'           // 守卫守护
];

// 投票类型
export const VOTE_TYPES = {
  EXILE: 'exile',     // 放逐投票
  POLICE: 'police',   // 警长选举
  SHERIFF: 'sheriff'  // 警徽传递
};

// 游戏事件类型
export const GAME_EVENTS = {
  PLAYER_SPEAK: 'player_speak',
  PLAYER_VOTE: 'player_vote',
  SKILL_USE: 'skill_use',
  PHASE_CHANGE: 'phase_change',
  PLAYER_DEATH: 'player_death',
  GAME_END: 'game_end'
};
