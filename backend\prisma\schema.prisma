// Prisma数据库模型定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id           String   @id @default(uuid()) @db.Uuid
  username     String   @unique @db.VarChar(50)
  email        String?  @unique @db.VarChar(100)
  passwordHash String?  @map("password_hash") @db.VarChar(255)
  avatarUrl    String?  @map("avatar_url") @db.VarChar(255)
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt    DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关联关系
  createdGames Game[]
  apiKeys      UserApiKey[]

  @@map("users")
}

// 游戏表
model Game {
  id             String    @id @default(uuid()) @db.Uuid
  name           String    @db.VarChar(100)
  status         String    @default("waiting") @db.VarChar(20) // waiting, playing, finished
  gameMode       String    @default("standard_12") @map("game_mode") @db.VarChar(50)
  maxPlayers     Int       @default(12) @map("max_players")
  currentPlayers Int       @default(0) @map("current_players")
  createdBy      String?   @map("created_by") @db.Uuid
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz
  startedAt      DateTime? @map("started_at") @db.Timestamptz
  finishedAt     DateTime? @map("finished_at") @db.Timestamptz
  winnerTeam     String?   @map("winner_team") @db.VarChar(20) // werewolf, villager, third_party
  gameData       Json?     @map("game_data") // 存储游戏状态和配置

  // 关联关系
  creator User?        @relation(fields: [createdBy], references: [id])
  players GamePlayer[]
  logs    GameLog[]

  @@map("games")
}

// 游戏玩家表
model GamePlayer {
  id           String   @id @default(uuid()) @db.Uuid
  gameId       String   @map("game_id") @db.Uuid
  playerNumber Int      @map("player_number") // 1-12
  playerType   String   @default("ai") @map("player_type") @db.VarChar(20) // ai, human
  aiModel      String?  @map("ai_model") @db.VarChar(50)
  role         String?  @db.VarChar(20) // werewolf, villager, prophet, etc.
  isAlive      Boolean  @default(true) @map("is_alive")
  isCaptain    Boolean  @default(false) @map("is_captain")
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz

  // 关联关系
  game Game @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@unique([gameId, playerNumber])
  @@map("game_players")
}

// 游戏日志表
model GameLog {
  id           String   @id @default(uuid()) @db.Uuid
  gameId       String   @map("game_id") @db.Uuid
  phase        String   @db.VarChar(20) // night, day, vote, etc.
  roundNumber  Int      @map("round_number")
  actionType   String   @map("action_type") @db.VarChar(50) // speak, vote, skill_use, etc.
  playerNumber Int?     @map("player_number")
  targetPlayer Int?     @map("target_player")
  content      String?  @db.Text
  metadata     Json?
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz

  // 关联关系
  game Game @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@map("game_logs")
}

// AI模型配置表
model AiModel {
  id            String   @id @default(uuid()) @db.Uuid
  name          String   @unique @db.VarChar(50)
  displayName   String   @map("display_name") @db.VarChar(100)
  provider      String   @db.VarChar(50) // openai, anthropic, google, etc.
  modelId       String   @map("model_id") @db.VarChar(100)
  apiEndpoint   String?  @map("api_endpoint") @db.VarChar(255)
  isActive      Boolean  @default(true) @map("is_active")
  defaultConfig Json?    @map("default_config")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamptz

  @@map("ai_models")
}

// 用户API密钥表
model UserApiKey {
  id               String   @id @default(uuid()) @db.Uuid
  userId           String   @map("user_id") @db.Uuid
  provider         String   @db.VarChar(50)
  apiKeyEncrypted  String   @map("api_key_encrypted") @db.Text
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt        DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, provider])
  @@map("user_api_keys")
}
