{"name": "werewolf-ai-backend", "version": "1.0.0", "description": "LLM狼人杀游戏后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:game": "ts-node src/test-game.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "ts-node src/utils/seed.ts"}, "keywords": ["werewolf", "llm", "ai", "game", "websocket"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "redis": "^4.6.10", "pg": "^8.11.3", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "axios": "^1.6.2", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "@google/generative-ai": "^0.1.3", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/pg": "^8.10.9", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}