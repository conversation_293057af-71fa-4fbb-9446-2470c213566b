# 🐳 LLM狼人杀游戏 - CentOS 7 部署教程

## 📋 当前环境信息
- **服务器**: CentOS 7 (Linux ECS5552 3.10.0-1160.119.1.el7.x86_64)
- **项目位置**: /root/llm
- **Docker**: 已安装

## 🛠️ 第一步：安装Docker Compose

```bash
# 进入项目目录
cd /root/llm

# 检查Docker版本
docker --version

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 给执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 创建软链接（如果需要）
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# 验证安装
docker-compose --version
```

## 🔧 第二步：配置环境变量

### 1. 配置后端环境变量
```bash
# 复制后端环境变量模板
cp backend/.env.example backend/.env

# 编辑后端环境变量
vi backend/.env
```

**后端 `.env` 配置内容：**
```env
# 服务器配置
NODE_ENV=production
PORT=8000

# 数据库配置
DATABASE_URL=*********************************************************/werewolf_db

# Redis配置
REDIS_URL=redis://redis:6379

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_2024
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=http://你的服务器IP:3000

# LLM API Keys - 请替换为你的真实API密钥
OPENAI_API_KEY=sk-your_openai_api_key_here
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here
SILICONFLOW_API_KEY=sk-your_siliconflow_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 游戏配置
DEFAULT_GAME_MODE=standard_12
MAX_GAMES_PER_USER=5
GAME_TIMEOUT_MINUTES=60

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. 配置前端环境变量
```bash
# 复制前端环境变量模板
cp frontend/.env.example frontend/.env.local

# 编辑前端环境变量
vi frontend/.env.local
```

**前端 `.env.local` 配置内容：**
```env
# API服务器地址 - 替换为你的服务器IP
NEXT_PUBLIC_API_URL=http://你的服务器IP:8000/api

# WebSocket服务器地址 - 替换为你的服务器IP
NEXT_PUBLIC_WS_URL=http://你的服务器IP:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=LLM狼人杀
NEXT_PUBLIC_APP_VERSION=1.0.0

# 生产模式配置
NEXT_PUBLIC_DEBUG=false
```

## 🔐 第三步：配置防火墙

```bash
# 检查防火墙状态
systemctl status firewalld

# 如果防火墙开启，需要开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp  # 前端
sudo firewall-cmd --permanent --add-port=8000/tcp  # 后端API
sudo firewall-cmd --permanent --add-port=80/tcp    # HTTP
sudo firewall-cmd --permanent --add-port=443/tcp   # HTTPS

# 重载防火墙配置
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

## 🚀 第四步：启动部署

### 1. 检查项目文件
```bash
# 确保在项目目录
cd /root/llm

# 查看项目结构
ls -la

# 应该看到以下文件/目录：
# - backend/
# - frontend/
# - shared/
# - docker-compose.yml
# - docker-compose.prod.yml
```

### 2. 构建并启动服务
```bash
# 拉取基础镜像（可选，加速构建）
docker pull node:18-alpine
docker pull postgres:15-alpine
docker pull redis:7-alpine
docker pull nginx:alpine

# 构建并启动所有服务（生产模式）
docker-compose -f docker-compose.prod.yml up -d --build

# 或者使用开发模式（如果没有prod文件）
docker-compose up -d --build
```

### 3. 监控启动过程
```bash
# 查看容器状态
docker-compose ps

# 查看启动日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

## ✅ 第五步：验证部署

### 1. 检查服务状态
```bash
# 检查所有容器是否运行
docker ps

# 检查后端健康状态
curl http://localhost:8000/api/health

# 检查前端是否可访问
curl -I http://localhost:3000

# 从外部检查（替换为你的服务器IP）
curl http://你的服务器IP:8000/api/health
curl -I http://你的服务器IP:3000
```

### 2. 测试数据库连接
```bash
# 进入数据库容器
docker-compose exec postgres psql -U werewolf_user -d werewolf_db

# 在数据库中执行测试查询
\dt  # 查看表
SELECT COUNT(*) FROM ai_models;  # 查看AI模型数量
\q   # 退出
```

### 3. 测试游戏功能
```bash
# 运行后端测试
docker-compose exec backend npm run test:game
```

## 🌐 第六步：访问应用

部署成功后，通过以下地址访问：

- **前端应用**: http://你的服务器IP:3000
- **后端API**: http://你的服务器IP:8000/api
- **健康检查**: http://你的服务器IP:8000/api/health

## 🔧 常用管理命令

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看最近100行日志
docker-compose logs --tail=100 backend

# 查看错误日志
docker-compose logs backend | grep ERROR
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend

# 重新构建并重启
docker-compose up -d --build backend
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷（注意：会删除数据）
docker-compose down -v
```

### 数据库管理
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U werewolf_user werewolf_db > /root/backup_$(date +%Y%m%d).sql

# 查看数据库大小
docker-compose exec postgres psql -U werewolf_user -d werewolf_db -c "SELECT pg_size_pretty(pg_database_size('werewolf_db'));"
```

## 🐛 故障排除

### 常见问题

**1. 容器启动失败**
```bash
# 查看详细错误信息
docker-compose logs <service_name>

# 检查端口占用
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# 杀死占用端口的进程
kill -9 <PID>
```

**2. 内存不足**
```bash
# 查看系统内存
free -h

# 查看Docker资源使用
docker stats

# 清理Docker缓存
docker system prune -a
```

**3. 磁盘空间不足**
```bash
# 查看磁盘使用
df -h

# 清理Docker
docker system prune -a --volumes

# 清理日志文件
journalctl --vacuum-time=7d
```

**4. 网络连接问题**
```bash
# 检查Docker网络
docker network ls

# 重建网络
docker-compose down
docker-compose up -d
```

## 📊 性能监控

### 1. 创建监控脚本
```bash
# 创建监控脚本
cat > /root/monitor.sh << 'EOF'
#!/bin/bash
echo "=== $(date) ==="
echo "容器状态:"
docker-compose ps

echo -e "\n系统资源:"
free -h
df -h /

echo -e "\nDocker资源使用:"
docker stats --no-stream

echo -e "\n服务健康检查:"
curl -s http://localhost:8000/api/health | jq .

echo "========================"
EOF

# 给执行权限
chmod +x /root/monitor.sh

# 运行监控
./monitor.sh
```

### 2. 设置定时任务
```bash
# 编辑crontab
crontab -e

# 添加以下内容：
# 每5分钟检查一次服务状态
*/5 * * * * /root/monitor.sh >> /var/log/werewolf_monitor.log 2>&1

# 每天凌晨2点备份数据库
0 2 * * * docker-compose exec -T postgres pg_dump -U werewolf_user werewolf_db > /root/backup_$(date +\%Y\%m\%d).sql
```

## 🔒 安全加固

### 1. 更新系统
```bash
# 更新系统包
yum update -y

# 安装安全工具
yum install -y fail2ban
```

### 2. 配置SSL（可选）
```bash
# 安装certbot
yum install -y epel-release
yum install -y certbot

# 获取SSL证书（需要域名）
# certbot certonly --standalone -d yourdomain.com
```

## 📝 维护建议

1. **定期备份数据库**
2. **监控系统资源使用**
3. **定期更新Docker镜像**
4. **查看应用日志**
5. **监控API密钥使用量**

## 🎯 下一步

部署完成后，你可以：

1. 访问 http://你的服务器IP:3000 开始使用
2. 配置你的LLM API密钥
3. 创建第一个游戏进行测试
4. 根据需要调整游戏配置

---

**注意**: 请将所有 "你的服务器IP" 替换为实际的服务器IP地址，并确保配置了有效的LLM API密钥。
