# 🔧 LLM狼人杀游戏 - 故障排除指南

## 🚨 常见问题快速解决

### 1. 容器启动失败

**问题**: 运行 `docker-compose up -d` 后容器无法启动

**解决步骤**:
```bash
# 查看容器状态
docker-compose ps

# 查看详细错误日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres

# 重新构建并启动
docker-compose down
docker-compose up -d --build
```

### 2. 端口被占用

**问题**: 提示端口3000或8000被占用

**解决步骤**:
```bash
# 查看端口占用
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# 杀死占用端口的进程
kill -9 <PID>

# 或者修改端口映射
vi docker-compose.yml
# 将 "3000:3000" 改为 "3001:3000"
```

### 3. 数据库连接失败

**问题**: 后端无法连接数据库

**解决步骤**:
```bash
# 检查数据库容器状态
docker-compose logs postgres

# 进入数据库容器测试连接
docker-compose exec postgres psql -U werewolf_user -d werewolf_db

# 如果连接失败，重置数据库
docker-compose down -v
docker-compose up -d postgres
# 等待30秒后启动其他服务
docker-compose up -d
```

### 4. API密钥配置问题

**问题**: AI模型调用失败

**解决步骤**:
```bash
# 检查环境变量是否正确加载
docker-compose exec backend printenv | grep API_KEY

# 编辑配置文件
vi backend/.env

# 确保API密钥格式正确，然后重启
docker-compose restart backend

# 测试API连接
docker-compose exec backend npm run test:game
```

### 5. 前端无法访问后端

**问题**: 前端页面显示连接错误

**解决步骤**:
```bash
# 检查前端环境变量
cat frontend/.env.local

# 确保API_URL配置正确
# NEXT_PUBLIC_API_URL=http://你的服务器IP:8000/api

# 检查防火墙
firewall-cmd --list-ports

# 如果端口未开放
firewall-cmd --permanent --add-port=8000/tcp
firewall-cmd --reload

# 重启前端服务
docker-compose restart frontend
```

### 6. 内存不足

**问题**: 服务器内存不足导致容器被杀死

**解决步骤**:
```bash
# 查看内存使用
free -h
docker stats

# 清理Docker缓存
docker system prune -a

# 清理系统缓存
echo 3 > /proc/sys/vm/drop_caches

# 如果内存仍然不足，考虑升级服务器配置
```

### 7. 磁盘空间不足

**问题**: 磁盘空间不足

**解决步骤**:
```bash
# 查看磁盘使用
df -h

# 清理Docker
docker system prune -a --volumes

# 清理日志
journalctl --vacuum-time=7d

# 清理临时文件
rm -rf /tmp/*
```

## 🔍 诊断命令

### 快速健康检查
```bash
#!/bin/bash
echo "=== 服务健康检查 ==="

# 检查容器状态
echo "1. 容器状态:"
docker-compose ps

# 检查端口监听
echo -e "\n2. 端口监听:"
netstat -tulpn | grep -E ":(3000|8000|5432|6379)"

# 检查API健康
echo -e "\n3. API健康检查:"
curl -s http://localhost:8000/api/health | jq . || echo "API不可用"

# 检查前端
echo -e "\n4. 前端检查:"
curl -I http://localhost:3000 2>/dev/null | head -1 || echo "前端不可用"

# 检查系统资源
echo -e "\n5. 系统资源:"
free -h
df -h /
```

### 详细日志分析
```bash
# 查看最近的错误日志
docker-compose logs --tail=50 | grep -i error

# 查看数据库日志
docker-compose logs postgres | grep -i error

# 查看后端启动日志
docker-compose logs backend | head -50

# 实时监控日志
docker-compose logs -f --tail=10
```

## 🛠️ 维护命令

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
docker-compose restart frontend

# 强制重新构建
docker-compose down
docker-compose up -d --build
```

### 数据库维护
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U werewolf_user werewolf_db > backup_$(date +%Y%m%d).sql

# 查看数据库大小
docker-compose exec postgres psql -U werewolf_user -d werewolf_db -c "SELECT pg_size_pretty(pg_database_size('werewolf_db'));"

# 清理数据库连接
docker-compose exec postgres psql -U werewolf_user -d werewolf_db -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'werewolf_db' AND pid <> pg_backend_pid();"
```

### 性能优化
```bash
# 查看资源使用
docker stats --no-stream

# 限制容器内存使用（编辑docker-compose.yml）
# 在服务下添加：
# deploy:
#   resources:
#     limits:
#       memory: 512M

# 清理未使用的镜像
docker image prune -a

# 清理未使用的卷
docker volume prune
```

## 📞 获取帮助

### 收集诊断信息
```bash
#!/bin/bash
# 创建诊断报告
REPORT_FILE="/root/diagnostic_$(date +%Y%m%d_%H%M%S).txt"

echo "LLM狼人杀游戏 - 诊断报告" > $REPORT_FILE
echo "生成时间: $(date)" >> $REPORT_FILE
echo "=================================" >> $REPORT_FILE

echo -e "\n1. 系统信息:" >> $REPORT_FILE
uname -a >> $REPORT_FILE
cat /etc/redhat-release >> $REPORT_FILE

echo -e "\n2. Docker版本:" >> $REPORT_FILE
docker --version >> $REPORT_FILE
docker-compose --version >> $REPORT_FILE

echo -e "\n3. 容器状态:" >> $REPORT_FILE
docker-compose ps >> $REPORT_FILE

echo -e "\n4. 系统资源:" >> $REPORT_FILE
free -h >> $REPORT_FILE
df -h >> $REPORT_FILE

echo -e "\n5. 网络端口:" >> $REPORT_FILE
netstat -tulpn | grep -E ":(3000|8000|5432|6379)" >> $REPORT_FILE

echo -e "\n6. 最近错误日志:" >> $REPORT_FILE
docker-compose logs --tail=100 | grep -i error >> $REPORT_FILE

echo "诊断报告已生成: $REPORT_FILE"
```

### 联系支持时提供的信息
1. 诊断报告文件
2. 具体的错误信息
3. 操作步骤
4. 服务器配置信息
5. 网络环境信息

## 🔄 完全重置

如果所有方法都无效，可以完全重置：

```bash
# 警告：这将删除所有数据！
cd /root/llm

# 停止并删除所有容器和数据
docker-compose down -v

# 删除所有相关镜像
docker rmi $(docker images | grep llm | awk '{print $3}')

# 清理系统
docker system prune -a --volumes

# 重新部署
./快速部署脚本.sh
```

---

**记住**: 在执行任何重置操作前，请先备份重要数据！
