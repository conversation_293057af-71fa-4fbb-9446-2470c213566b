import { EventEmitter } from 'events';
import { GameState, Player, GameAction, GamePhase, Role, GameConfig } from '../../../shared/types/game';
import { DEFAULT_GAME_CONFIGS, ROLE_INFO, WIN_CONDITIONS } from '../../../shared/constants/game';
import { RoleSystem, NightAction, RoleEffect } from './RoleSystem';

export class GameEngine extends EventEmitter {
  private gameState: GameState;
  private config: GameConfig;
  private phaseTimer?: NodeJS.Timeout;
  private roleSystem: RoleSystem;
  private nightActions: NightAction[] = [];

  constructor(gameId: string, gameName: string, gameMode: string = 'standard_12') {
    super();

    this.config = DEFAULT_GAME_CONFIGS[gameMode] || DEFAULT_GAME_CONFIGS.standard_12;
    this.roleSystem = new RoleSystem();
    
    this.gameState = {
      id: gameId,
      name: gameName,
      status: 'waiting',
      mode: this.config.mode,
      config: this.config,
      players: [],
      currentPhase: 'preparation',
      currentRound: 0,
      timeRemaining: 0,
      createdAt: new Date().toISOString()
    };
  }

  // 获取游戏状态
  getGameState(): GameState {
    return { ...this.gameState };
  }

  // 添加玩家
  addPlayer(playerType: 'ai' | 'human', aiModel?: string): Player | null {
    if (this.gameState.players.length >= this.config.maxPlayers) {
      throw new Error('游戏人数已满');
    }

    if (this.gameState.status !== 'waiting') {
      throw new Error('游戏已开始，无法加入');
    }

    const playerNumber = this.gameState.players.length + 1;
    const player: Player = {
      id: `player-${playerNumber}`,
      playerNumber,
      playerType,
      aiModel,
      isAlive: true,
      isCaptain: false,
      votes: 0,
      hasVoted: false
    };

    this.gameState.players.push(player);
    this.emit('playerJoined', player);
    
    return player;
  }

  // 开始游戏
  startGame(): boolean {
    if (this.gameState.status !== 'waiting') {
      throw new Error('游戏已经开始');
    }

    if (this.gameState.players.length !== this.config.maxPlayers) {
      throw new Error(`需要${this.config.maxPlayers}名玩家才能开始游戏`);
    }

    // 分配角色
    this.assignRoles();
    
    // 更新游戏状态
    this.gameState.status = 'playing';
    this.gameState.startedAt = new Date().toISOString();
    this.gameState.currentRound = 1;
    
    // 开始第一个夜晚
    this.startPhase('night');
    
    this.emit('gameStarted', this.gameState);
    return true;
  }

  // 分配角色
  private assignRoles(): void {
    const roles = [...this.config.roles];
    const shuffledRoles = this.shuffleArray(roles);
    
    this.gameState.players.forEach((player, index) => {
      player.role = shuffledRoles[index];
    });

    // 如果有警长选举，随机选择一个玩家作为临时警长候选
    if (this.config.rules.hasPolice) {
      // 警长将在白天阶段选举
    }
  }

  // 开始新阶段
  private startPhase(phase: GamePhase): void {
    this.gameState.currentPhase = phase;
    
    // 清除之前的计时器
    if (this.phaseTimer) {
      clearTimeout(this.phaseTimer);
    }

    // 设置阶段时间
    let phaseDuration = 0;
    switch (phase) {
      case 'night':
        phaseDuration = this.config.timeSettings.nightPhase;
        break;
      case 'day':
        phaseDuration = this.config.timeSettings.dayPhase;
        break;
      case 'vote':
        phaseDuration = this.config.timeSettings.votePhase;
        break;
      default:
        phaseDuration = 30; // 默认30秒
    }

    this.gameState.timeRemaining = phaseDuration;
    
    // 启动计时器
    this.phaseTimer = setTimeout(() => {
      this.endPhase();
    }, phaseDuration * 1000);

    this.emit('phaseChanged', {
      phase,
      timeRemaining: phaseDuration,
      round: this.gameState.currentRound
    });
  }

  // 结束当前阶段
  private endPhase(): void {
    const currentPhase = this.gameState.currentPhase;
    
    switch (currentPhase) {
      case 'night':
        this.processNightActions();
        this.startPhase('day');
        break;
      case 'day':
        this.startPhase('vote');
        break;
      case 'vote':
        this.processVoteResults();
        if (this.checkWinCondition()) {
          this.endGame();
        } else {
          this.gameState.currentRound++;
          this.startPhase('night');
        }
        break;
    }
  }

  // 处理夜晚行动
  private processNightActions(): void {
    // 使用角色系统处理夜晚行动
    const effects = this.roleSystem.processNightActions(this.nightActions, this.gameState);

    // 应用效果
    this.applyEffects(effects);

    // 清空夜晚行动
    this.nightActions = [];

    this.emit('nightActionsProcessed', {
      round: this.gameState.currentRound,
      effects
    });
  }

  // 应用角色效果
  private applyEffects(effects: RoleEffect[]): void {
    effects.forEach(effect => {
      const targetPlayer = this.gameState.players.find(p => p.playerNumber === effect.target);
      if (!targetPlayer) return;

      switch (effect.type) {
        case 'kill':
        case 'poison':
          this.eliminatePlayer(effect.target, effect.type);
          break;
        case 'save':
          // 救活玩家（如果在同一轮被杀）
          targetPlayer.isAlive = true;
          break;
        case 'guard':
          // 守护效果在resolveEffects中已处理
          break;
        case 'check':
          // 查验结果通过事件发送给AI
          this.emit('prophetCheck', {
            prophet: effect.source,
            target: effect.target,
            result: effect.metadata?.result
          });
          break;
        case 'reveal':
          // 白痴翻牌
          targetPlayer.isAlive = true;
          if (!targetPlayer.metadata) targetPlayer.metadata = {};
          targetPlayer.metadata.revealed = true;
          break;
      }
    });
  }

  // 处理投票结果
  private processVoteResults(): void {
    // 统计投票
    const voteCounts: { [playerNumber: number]: number } = {};
    
    this.gameState.players.forEach(player => {
      if (player.isAlive && player.votedFor) {
        voteCounts[player.votedFor] = (voteCounts[player.votedFor] || 0) + 1;
      }
    });

    // 找出得票最多的玩家
    let maxVotes = 0;
    let eliminatedPlayer: number | null = null;
    
    Object.entries(voteCounts).forEach(([playerNumber, votes]) => {
      if (votes > maxVotes) {
        maxVotes = votes;
        eliminatedPlayer = parseInt(playerNumber);
      }
    });

    // 处理淘汰
    if (eliminatedPlayer) {
      this.eliminatePlayer(eliminatedPlayer, 'voted_out');
    }

    // 重置投票状态
    this.gameState.players.forEach(player => {
      player.hasVoted = false;
      player.votedFor = undefined;
      player.votes = 0;
    });
  }

  // 淘汰玩家
  private eliminatePlayer(playerNumber: number, reason: string): void {
    const player = this.gameState.players.find(p => p.playerNumber === playerNumber);
    if (player) {
      player.isAlive = false;
      
      // 处理特殊角色的死亡技能
      this.handleDeathSkills(player, reason);
      
      this.emit('playerEliminated', {
        playerNumber,
        role: player.role,
        reason
      });
    }
  }

  // 处理死亡技能
  private handleDeathSkills(player: Player, reason: string): void {
    if (!player.role) return;

    switch (player.role) {
      case 'hunter':
        // 猎人可以开枪（除非被毒杀）
        if (reason !== 'poisoned') {
          this.emit('hunterShoot', { playerNumber: player.playerNumber });
        }
        break;
      case 'wolf_king':
        // 狼王可以开枪（除非被毒杀或殉情）
        if (reason !== 'poisoned' && reason !== 'suicide') {
          this.emit('wolfKingShoot', { playerNumber: player.playerNumber });
        }
        break;
      case 'idiot':
        // 白痴被投票时可以翻牌
        if (reason === 'voted_out') {
          player.isAlive = true; // 免疫此次放逐
          this.emit('idiotRevealed', { playerNumber: player.playerNumber });
        }
        break;
    }
  }

  // 检查胜利条件
  private checkWinCondition(): boolean {
    const alivePlayers = this.gameState.players.filter(p => p.isAlive);
    const aliveWerewolves = alivePlayers.filter(p => 
      p.role && ROLE_INFO[p.role].team === 'werewolf'
    );
    const aliveVillagers = alivePlayers.filter(p => 
      p.role && ROLE_INFO[p.role].team === 'villager'
    );
    const aliveGods = aliveVillagers.filter(p => 
      p.role && p.role !== 'villager'
    );
    const aliveCivilians = aliveVillagers.filter(p => 
      p.role === 'villager'
    );

    // 狼人胜利条件：屠边（杀死所有神职或所有平民）
    if (aliveWerewolves.length > 0 && (aliveGods.length === 0 || aliveCivilians.length === 0)) {
      this.gameState.winner = 'werewolf';
      return true;
    }

    // 好人胜利条件：所有狼人死亡
    if (aliveWerewolves.length === 0) {
      this.gameState.winner = 'villager';
      return true;
    }

    return false;
  }

  // 结束游戏
  private endGame(): void {
    this.gameState.status = 'finished';
    this.gameState.finishedAt = new Date().toISOString();
    this.gameState.currentPhase = 'finished';
    
    if (this.phaseTimer) {
      clearTimeout(this.phaseTimer);
    }

    this.emit('gameEnded', {
      winner: this.gameState.winner,
      gameState: this.gameState
    });
  }

  // 执行游戏行动
  executeAction(action: GameAction): boolean {
    const player = this.gameState.players.find(p => p.playerNumber === action.playerNumber);
    if (!player || !player.isAlive) {
      return false;
    }

    switch (action.actionType) {
      case 'speak':
        return this.handleSpeak(action);
      case 'vote':
        return this.handleVote(action);
      case 'skill_use':
        return this.handleSkillUse(action);
      default:
        return false;
    }
  }

  // 处理发言
  private handleSpeak(action: GameAction): boolean {
    if (this.gameState.currentPhase !== 'day') {
      return false;
    }

    this.emit('playerSpeak', {
      playerNumber: action.playerNumber,
      content: action.content,
      timestamp: action.timestamp
    });

    return true;
  }

  // 处理投票
  private handleVote(action: GameAction): boolean {
    if (this.gameState.currentPhase !== 'vote') {
      return false;
    }

    const player = this.gameState.players.find(p => p.playerNumber === action.playerNumber);
    const target = this.gameState.players.find(p => p.playerNumber === action.targetPlayer);

    if (!player || !target || player.hasVoted) {
      return false;
    }

    player.hasVoted = true;
    player.votedFor = action.targetPlayer;
    if (target) {
      target.votes++;
    }

    this.emit('playerVote', {
      playerNumber: action.playerNumber,
      targetPlayer: action.targetPlayer,
      timestamp: action.timestamp
    });

    return true;
  }

  // 处理技能使用
  private handleSkillUse(action: GameAction): boolean {
    const player = this.gameState.players.find(p => p.playerNumber === action.playerNumber);
    if (!player || !player.role) {
      return false;
    }

    // 检查是否可以使用技能
    if (!this.roleSystem.canUseSkill(player, action.metadata?.skillName, this.gameState)) {
      return false;
    }

    // 如果是夜晚阶段，添加到夜晚行动列表
    if (this.gameState.currentPhase === 'night') {
      const nightAction: NightAction = {
        playerNumber: action.playerNumber!,
        role: player.role,
        skillName: action.metadata?.skillName,
        target: action.targetPlayer,
        metadata: action.metadata
      };

      this.nightActions.push(nightAction);
    } else {
      // 立即执行技能（如猎人开枪）
      const target = action.targetPlayer ?
        this.gameState.players.find(p => p.playerNumber === action.targetPlayer) : null;

      const result = this.roleSystem.executeSkill(
        player,
        action.metadata?.skillName,
        target,
        this.gameState
      );

      if (result.success) {
        this.applyEffects(result.effects);
      }
    }

    this.emit('skillUsed', {
      playerNumber: action.playerNumber,
      skillName: action.metadata?.skillName,
      targetPlayer: action.targetPlayer,
      timestamp: action.timestamp
    });

    return true;
  }

  // 工具方法：数组洗牌
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  // 清理资源
  destroy(): void {
    if (this.phaseTimer) {
      clearTimeout(this.phaseTimer);
    }
    this.removeAllListeners();
  }
}
