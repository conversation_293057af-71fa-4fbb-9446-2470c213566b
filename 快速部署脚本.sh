#!/bin/bash

# LLM狼人杀游戏 - CentOS 7 快速部署脚本
# 使用方法: chmod +x 快速部署脚本.sh && ./快速部署脚本.sh

set -e  # 遇到错误立即退出

echo "🐺 LLM狼人杀游戏 - 自动部署脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root用户运行此脚本"
    exit 1
fi

# 检查当前目录
if [ ! -f "docker-compose.yml" ]; then
    log_error "请在项目根目录(/root/llm)运行此脚本"
    exit 1
fi

log_info "开始部署准备..."

# 1. 安装Docker Compose
log_info "检查Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    log_info "安装Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    log_info "Docker Compose安装完成"
else
    log_info "Docker Compose已安装: $(docker-compose --version)"
fi

# 2. 获取服务器IP
SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || hostname -I | awk '{print $1}')
log_info "检测到服务器IP: $SERVER_IP"

# 3. 配置环境变量
log_info "配置环境变量..."

# 后端环境变量
if [ ! -f "backend/.env" ]; then
    log_info "创建后端环境变量文件..."
    cat > backend/.env << EOF
# 服务器配置
NODE_ENV=production
PORT=8000

# 数据库配置
DATABASE_URL=*********************************************************/werewolf_db

# Redis配置
REDIS_URL=redis://redis:6379

# JWT配置
JWT_SECRET=werewolf_jwt_secret_$(openssl rand -hex 16)
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=http://$SERVER_IP:3000

# LLM API Keys - 请手动配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 游戏配置
DEFAULT_GAME_MODE=standard_12
MAX_GAMES_PER_USER=5
GAME_TIMEOUT_MINUTES=60

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF
    log_info "后端环境变量文件已创建"
else
    log_warn "后端环境变量文件已存在，跳过创建"
fi

# 前端环境变量
if [ ! -f "frontend/.env.local" ]; then
    log_info "创建前端环境变量文件..."
    cat > frontend/.env.local << EOF
# API服务器地址
NEXT_PUBLIC_API_URL=http://$SERVER_IP:8000/api

# WebSocket服务器地址
NEXT_PUBLIC_WS_URL=http://$SERVER_IP:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=LLM狼人杀
NEXT_PUBLIC_APP_VERSION=1.0.0

# 生产模式配置
NEXT_PUBLIC_DEBUG=false
EOF
    log_info "前端环境变量文件已创建"
else
    log_warn "前端环境变量文件已存在，跳过创建"
fi

# 4. 配置防火墙
log_info "配置防火墙..."
if systemctl is-active --quiet firewalld; then
    firewall-cmd --permanent --add-port=3000/tcp
    firewall-cmd --permanent --add-port=8000/tcp
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp
    firewall-cmd --reload
    log_info "防火墙端口已开放"
else
    log_warn "防火墙未运行，跳过配置"
fi

# 5. 创建必要目录
log_info "创建必要目录..."
mkdir -p logs
mkdir -p backup

# 6. 拉取Docker镜像
log_info "拉取Docker镜像..."
docker pull node:18-alpine
docker pull postgres:15-alpine
docker pull redis:7-alpine
docker pull nginx:alpine

# 7. 构建并启动服务
log_info "构建并启动服务..."
docker-compose down 2>/dev/null || true
docker-compose up -d --build

# 8. 等待服务启动
log_info "等待服务启动..."
sleep 30

# 9. 检查服务状态
log_info "检查服务状态..."
docker-compose ps

# 10. 健康检查
log_info "执行健康检查..."
for i in {1..10}; do
    if curl -f http://localhost:8000/api/health >/dev/null 2>&1; then
        log_info "后端服务健康检查通过"
        break
    else
        log_warn "等待后端服务启动... ($i/10)"
        sleep 5
    fi
done

for i in {1..10}; do
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        log_info "前端服务健康检查通过"
        break
    else
        log_warn "等待前端服务启动... ($i/10)"
        sleep 5
    fi
done

# 11. 创建监控脚本
log_info "创建监控脚本..."
cat > /root/werewolf_monitor.sh << 'EOF'
#!/bin/bash
echo "=== $(date) ==="
echo "容器状态:"
cd /root/llm && docker-compose ps

echo -e "\n系统资源:"
free -h
df -h /

echo -e "\nDocker资源使用:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo -e "\n服务健康检查:"
curl -s http://localhost:8000/api/health 2>/dev/null || echo "后端服务不可用"

echo "========================"
EOF

chmod +x /root/werewolf_monitor.sh

# 12. 创建备份脚本
log_info "创建备份脚本..."
cat > /root/werewolf_backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/root/backup"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

cd /root/llm

# 备份数据库
docker-compose exec -T postgres pg_dump -U werewolf_user werewolf_db > $BACKUP_DIR/db_$DATE.sql

# 备份配置文件
cp backend/.env $BACKUP_DIR/backend_env_$DATE
cp frontend/.env.local $BACKUP_DIR/frontend_env_$DATE

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/db_$DATE.sql"
EOF

chmod +x /root/werewolf_backup.sh

# 13. 设置定时任务
log_info "设置定时任务..."
(crontab -l 2>/dev/null; echo "*/5 * * * * /root/werewolf_monitor.sh >> /var/log/werewolf_monitor.log 2>&1") | crontab -
(crontab -l 2>/dev/null; echo "0 2 * * * /root/werewolf_backup.sh >> /var/log/werewolf_backup.log 2>&1") | crontab -

# 14. 显示部署结果
echo ""
echo "🎉 部署完成！"
echo "=================================="
echo "前端地址: http://$SERVER_IP:3000"
echo "后端API: http://$SERVER_IP:8000/api"
echo "健康检查: http://$SERVER_IP:8000/api/health"
echo ""
echo "⚠️  重要提醒："
echo "1. 请编辑 backend/.env 文件，配置你的LLM API密钥"
echo "2. 配置完成后运行: docker-compose restart backend"
echo "3. 监控脚本: /root/werewolf_monitor.sh"
echo "4. 备份脚本: /root/werewolf_backup.sh"
echo ""
echo "📋 常用命令："
echo "查看日志: docker-compose logs -f"
echo "重启服务: docker-compose restart"
echo "停止服务: docker-compose down"
echo "查看状态: docker-compose ps"
echo ""

# 15. 显示API密钥配置提醒
echo "🔑 API密钥配置："
echo "编辑配置文件: vi backend/.env"
echo "需要配置的密钥："
echo "- OPENAI_API_KEY (OpenAI GPT模型)"
echo "- ANTHROPIC_API_KEY (Claude模型)"
echo "- SILICONFLOW_API_KEY (硅基流动模型)"
echo "- GOOGLE_API_KEY (Gemini模型)"
echo ""
echo "配置完成后重启: docker-compose restart backend"

log_info "部署脚本执行完成！"
