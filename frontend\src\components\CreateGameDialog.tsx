'use client';

import React, { useState } from 'react';
import { But<PERSON> } from './ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';

interface CreateGameDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateGame: (gameData: GameCreationData) => void;
}

interface GameCreationData {
  name: string;
  gameMode: string;
  aiModels: string[];
}

const AI_MODELS = [
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI' },
  { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI' },
  { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', provider: 'Anthropic' },
  { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', provider: 'Anthropic' },
  { id: 'Qwen/Qwen2.5-7B-Instruct', name: 'Qwen2.5-7B', provider: '硅基流动' },
  { id: 'Qwen/Qwen2.5-14B-Instruct', name: 'Qwen2.5-14B', provider: '硅基流动' },
  { id: 'THUDM/glm-4-9b-chat', name: 'GLM-4-9B', provider: '硅基流动' },
  { id: 'meta-llama/Meta-Llama-3.1-8B-Instruct', name: 'Llama-3.1-8B', provider: '硅基流动' },
];

const GAME_MODES = [
  { id: 'standard_12', name: '12人标准场', description: '4狼人 + 4神职 + 4村民' },
  { id: 'advanced_12', name: '12人进阶场', description: '1狼王+3狼人 + 4神职 + 4村民' },
];

export const CreateGameDialog: React.FC<CreateGameDialogProps> = ({
  isOpen,
  onClose,
  onCreateGame
}) => {
  const [formData, setFormData] = useState({
    name: '',
    gameMode: 'standard_12',
    selectedModels: [] as string[]
  });
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleModelToggle = (modelId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedModels: prev.selectedModels.includes(modelId)
        ? prev.selectedModels.filter(id => id !== modelId)
        : [...prev.selectedModels, modelId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('请输入游戏名称');
      return;
    }

    if (formData.selectedModels.length === 0) {
      alert('请至少选择一个AI模型');
      return;
    }

    setLoading(true);
    try {
      await onCreateGame({
        name: formData.name.trim(),
        gameMode: formData.gameMode,
        aiModels: formData.selectedModels
      });
      
      // 重置表单
      setFormData({
        name: '',
        gameMode: 'standard_12',
        selectedModels: []
      });
      onClose();
    } catch (error) {
      console.error('创建游戏失败:', error);
      alert('创建游戏失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>创建新游戏</CardTitle>
            <Button variant="ghost" onClick={onClose} className="p-2">
              ✕
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 游戏名称 */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                游戏名称
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="输入游戏名称..."
                maxLength={50}
              />
            </div>

            {/* 游戏模式 */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                游戏模式
              </label>
              <div className="space-y-2">
                {GAME_MODES.map((mode) => (
                  <label
                    key={mode.id}
                    className="flex items-center p-3 bg-white/5 border border-white/10 rounded-lg cursor-pointer hover:bg-white/10 transition-colors"
                  >
                    <input
                      type="radio"
                      name="gameMode"
                      value={mode.id}
                      checked={formData.gameMode === mode.id}
                      onChange={(e) => setFormData(prev => ({ ...prev, gameMode: e.target.value }))}
                      className="mr-3"
                    />
                    <div>
                      <div className="text-white font-medium">{mode.name}</div>
                      <div className="text-gray-400 text-sm">{mode.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* AI模型选择 */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                选择AI模型 ({formData.selectedModels.length}/12)
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                {AI_MODELS.map((model) => (
                  <label
                    key={model.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.selectedModels.includes(model.id)
                        ? 'bg-purple-600/20 border-purple-500'
                        : 'bg-white/5 border-white/10 hover:bg-white/10'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.selectedModels.includes(model.id)}
                      onChange={() => handleModelToggle(model.id)}
                      className="mr-3"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-white font-medium truncate">{model.name}</div>
                      <div className="text-gray-400 text-sm">{model.provider}</div>
                    </div>
                  </label>
                ))}
              </div>
              <p className="text-gray-400 text-sm mt-2">
                💡 选择的模型将随机分配给12个AI玩家，可以重复选择同一模型
              </p>
            </div>

            {/* 按钮 */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                className="flex-1"
                disabled={loading}
              >
                取消
              </Button>
              <Button
                type="submit"
                className="flex-1"
                loading={loading}
                disabled={!formData.name.trim() || formData.selectedModels.length === 0}
              >
                创建游戏
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
