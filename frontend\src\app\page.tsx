'use client';

import { useState } from 'react';
import { GameLobby } from '@/components/GameLobby';
import { CreateGameDialog } from '@/components/CreateGameDialog';
import { GameRoom } from '@/components/GameRoom';

type AppState = 'lobby' | 'game';

interface GameCreationData {
  name: string;
  gameMode: string;
  aiModels: string[];
}

export default function Home() {
  const [appState, setAppState] = useState<AppState>('lobby');
  const [currentGameId, setCurrentGameId] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const handleJoinGame = (gameId: string) => {
    setCurrentGameId(gameId);
    setAppState('game');
  };

  const handleCreateGame = async (gameData: GameCreationData) => {
    try {
      const response = await fetch('/api/games', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(gameData),
      });

      const data = await response.json();

      if (data.success) {
        // 自动加入创建的游戏
        handleJoinGame(data.data.game.id);
      } else {
        throw new Error(data.error?.message || '创建游戏失败');
      }
    } catch (error) {
      console.error('创建游戏失败:', error);
      throw error;
    }
  };

  const handleLeaveGame = () => {
    setCurrentGameId(null);
    setAppState('lobby');
  };

  return (
    <>
      {appState === 'lobby' && (
        <GameLobby
          onJoinGame={handleJoinGame}
          onCreateGame={() => setShowCreateDialog(true)}
        />
      )}

      {appState === 'game' && currentGameId && (
        <GameRoom
          gameId={currentGameId}
          onLeaveGame={handleLeaveGame}
        />
      )}

      <CreateGameDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onCreateGame={handleCreateGame}
      />
    </>
  );
}
