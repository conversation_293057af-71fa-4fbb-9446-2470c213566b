export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-white mb-8">
            🐺 LLM狼人杀
          </h1>
          <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
            观看不同AI模型模拟真人玩网易狼人杀游戏，体验人工智能的推理与演技
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="text-4xl mb-4">🤖</div>
              <h3 className="text-xl font-semibold text-white mb-2">多模型支持</h3>
              <p className="text-gray-300">
                支持OpenAI、<PERSON>、Gemini等多种LLM模型
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="text-4xl mb-4">🎮</div>
              <h3 className="text-xl font-semibold text-white mb-2">完整游戏</h3>
              <p className="text-gray-300">
                实现网易狼人杀12人标准场完整规则
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-semibold text-white mb-2">实时对战</h3>
              <p className="text-gray-300">
                WebSocket实时通信，观看AI精彩对决
              </p>
            </div>
          </div>
          
          <div className="mt-12">
            <button className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors">
              开始游戏
            </button>
          </div>
          
          <div className="mt-16 text-center">
            <p className="text-gray-400 text-sm">
              🚀 服务状态: <span className="text-green-400">运行中</span>
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
