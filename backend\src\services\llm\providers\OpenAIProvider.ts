import OpenAI from 'openai';
import { LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMError, LLMRateLimitError, LLMAuthError } from '../LLMProvider';

export class OpenAIProvider extends LLMProvider {
  private client: OpenAI;

  constructor(apiKey: string, model: string = 'gpt-3.5-turbo', config: LLMConfig = {}) {
    super(apiKey, model, {
      temperature: 0.8,
      maxTokens: 1000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      ...config
    });

    this.client = new OpenAI({
      apiKey: this.apiKey,
      timeout: 30000, // 30秒超时
    });
  }

  async sendMessage(messages: LLMMessage[], config?: LLMConfig): Promise<LLMResponse> {
    this.validateMessages(messages);
    const finalConfig = this.mergeConfig(config);

    try {
      return await this.withRetry(async () => {
        this.log('info', `发送消息到OpenAI`, { 
          model: this.model, 
          messageCount: messages.length,
          config: finalConfig 
        });

        const response = await this.client.chat.completions.create({
          model: this.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          temperature: finalConfig.temperature,
          max_tokens: finalConfig.maxTokens,
          top_p: finalConfig.topP,
          frequency_penalty: finalConfig.frequencyPenalty,
          presence_penalty: finalConfig.presencePenalty,
        });

        const choice = response.choices[0];
        if (!choice || !choice.message) {
          throw new LLMError('OpenAI返回了空响应', 'openai', this.model);
        }

        const result: LLMResponse = {
          content: choice.message.content || '',
          usage: response.usage ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens
          } : undefined,
          model: response.model,
          finishReason: choice.finish_reason || undefined
        };

        this.log('info', `OpenAI响应成功`, { 
          model: this.model,
          usage: result.usage,
          finishReason: result.finishReason
        });

        return result;
      });
    } catch (error: any) {
      this.log('error', `OpenAI调用失败`, { error: error.message });
      
      if (error instanceof LLMError) {
        throw error;
      }

      // 处理OpenAI特定错误
      if (error.status) {
        switch (error.status) {
          case 401:
            throw new LLMAuthError('openai', this.model);
          case 429:
            const retryAfter = error.headers?.['retry-after'] ? 
              parseInt(error.headers['retry-after']) : undefined;
            throw new LLMRateLimitError('openai', this.model, retryAfter);
          case 400:
            throw new LLMError(`请求参数错误: ${error.message}`, 'openai', this.model, 'BAD_REQUEST');
          case 404:
            throw new LLMError(`模型不存在: ${this.model}`, 'openai', this.model, 'MODEL_NOT_FOUND');
          default:
            throw new LLMError(`OpenAI API错误: ${error.message}`, 'openai', this.model, error.status.toString());
        }
      }

      this.handleError(error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const testMessages: LLMMessage[] = [
        { role: 'user', content: 'Hello, this is a connection test.' }
      ];

      await this.sendMessage(testMessages, { maxTokens: 10 });
      return true;
    } catch (error) {
      this.log('error', `OpenAI连接测试失败`, { error: (error as Error).message });
      return false;
    }
  }

  getModelInfo() {
    const modelInfo: { [key: string]: any } = {
      'gpt-4': {
        name: 'GPT-4',
        provider: 'openai',
        maxTokens: 8192,
        supportedFeatures: ['chat', 'function_calling', 'system_message']
      },
      'gpt-4-turbo': {
        name: 'GPT-4 Turbo',
        provider: 'openai',
        maxTokens: 128000,
        supportedFeatures: ['chat', 'function_calling', 'system_message', 'json_mode']
      },
      'gpt-3.5-turbo': {
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        maxTokens: 4096,
        supportedFeatures: ['chat', 'function_calling', 'system_message']
      },
      'gpt-3.5-turbo-16k': {
        name: 'GPT-3.5 Turbo 16K',
        provider: 'openai',
        maxTokens: 16384,
        supportedFeatures: ['chat', 'function_calling', 'system_message']
      }
    };

    return modelInfo[this.model] || {
      name: this.model,
      provider: 'openai',
      maxTokens: 4096,
      supportedFeatures: ['chat']
    };
  }

  // OpenAI特有方法：流式响应
  async sendMessageStream(
    messages: LLMMessage[], 
    config?: LLMConfig,
    onChunk?: (chunk: string) => void
  ): Promise<LLMResponse> {
    this.validateMessages(messages);
    const finalConfig = this.mergeConfig(config);

    try {
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: finalConfig.temperature,
        max_tokens: finalConfig.maxTokens,
        top_p: finalConfig.topP,
        frequency_penalty: finalConfig.frequencyPenalty,
        presence_penalty: finalConfig.presencePenalty,
        stream: true,
      });

      let fullContent = '';
      let usage: any = undefined;
      let finishReason: string | undefined = undefined;

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          fullContent += delta.content;
          if (onChunk) {
            onChunk(delta.content);
          }
        }

        if (chunk.choices[0]?.finish_reason) {
          finishReason = chunk.choices[0].finish_reason;
        }

        // 注意：流式响应通常不包含usage信息
      }

      return {
        content: fullContent,
        usage,
        model: this.model,
        finishReason
      };
    } catch (error) {
      this.log('error', `OpenAI流式调用失败`, { error: (error as Error).message });
      this.handleError(error);
    }
  }

  // 获取可用模型列表
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      this.log('error', `获取OpenAI模型列表失败`, { error: (error as Error).message });
      return ['gpt-3.5-turbo', 'gpt-4']; // 返回默认模型列表
    }
  }

  // 计算消息token数量（使用tiktoken库会更准确，这里简化处理）
  estimateMessageTokens(messages: LLMMessage[]): number {
    let totalTokens = 0;
    
    for (const message of messages) {
      // 每条消息的基础开销
      totalTokens += 4;
      
      // 内容token
      totalTokens += this.estimateTokens(message.content);
      
      // 角色token
      totalTokens += this.estimateTokens(message.role);
    }
    
    // 对话结束标记
    totalTokens += 2;
    
    return totalTokens;
  }
}
