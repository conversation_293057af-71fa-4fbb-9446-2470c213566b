import { Role, Player, GameAction } from '../../../shared/types/game';
import { ROLE_INFO, NIGHT_ACTION_ORDER } from '../../../shared/constants/game';

// 角色技能接口
export interface RoleSkill {
  name: string;
  description: string;
  canUse(player: Player, gameState: any): boolean;
  execute(player: Player, target: Player | null, gameState: any): RoleSkillResult;
}

// 技能执行结果
export interface RoleSkillResult {
  success: boolean;
  message: string;
  effects: RoleEffect[];
}

// 角色效果
export interface RoleEffect {
  type: 'kill' | 'save' | 'poison' | 'guard' | 'check' | 'reveal' | 'silence';
  target: number;
  source: number;
  metadata?: any;
}

// 夜晚行动
export interface NightAction {
  playerNumber: number;
  role: Role;
  skillName: string;
  target?: number;
  metadata?: any;
}

export class RoleSystem {
  private skills: Map<Role, RoleSkill[]> = new Map();

  constructor() {
    this.initializeSkills();
  }

  // 初始化所有角色技能
  private initializeSkills(): void {
    // 狼人技能
    this.skills.set('werewolf', [
      {
        name: 'kill',
        description: '夜晚杀死一名玩家',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && player.isAlive;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          return {
            success: true,
            message: `${player.playerNumber}号选择杀死${target.playerNumber}号`,
            effects: [{
              type: 'kill',
              target: target.playerNumber,
              source: player.playerNumber
            }]
          };
        }
      }
    ]);

    // 预言家技能
    this.skills.set('prophet', [
      {
        name: 'check',
        description: '查验一名玩家的身份',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && player.isAlive;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          
          const targetRole = target.role;
          const isWerewolf = targetRole && ROLE_INFO[targetRole].team === 'werewolf';
          
          return {
            success: true,
            message: `${target.playerNumber}号是${isWerewolf ? '狼人' : '好人'}`,
            effects: [{
              type: 'check',
              target: target.playerNumber,
              source: player.playerNumber,
              metadata: { result: isWerewolf ? 'werewolf' : 'villager' }
            }]
          };
        }
      }
    ]);

    // 女巫技能
    this.skills.set('witch', [
      {
        name: 'save',
        description: '使用解药救活被杀的玩家',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && 
                 player.isAlive && 
                 !player.metadata?.saveUsed;
        },
        execute: (player, target, gameState) => {
          return {
            success: true,
            message: '女巫使用了解药',
            effects: [{
              type: 'save',
              target: target?.playerNumber || 0,
              source: player.playerNumber
            }]
          };
        }
      },
      {
        name: 'poison',
        description: '使用毒药毒死一名玩家',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && 
                 player.isAlive && 
                 !player.metadata?.poisonUsed;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          return {
            success: true,
            message: `女巫毒死了${target.playerNumber}号`,
            effects: [{
              type: 'poison',
              target: target.playerNumber,
              source: player.playerNumber
            }]
          };
        }
      }
    ]);

    // 猎人技能
    this.skills.set('hunter', [
      {
        name: 'shoot',
        description: '死后开枪带走一名玩家',
        canUse: (player, gameState) => {
          return !player.isAlive && !player.metadata?.shotUsed;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          return {
            success: true,
            message: `猎人开枪带走了${target.playerNumber}号`,
            effects: [{
              type: 'kill',
              target: target.playerNumber,
              source: player.playerNumber,
              metadata: { reason: 'hunter_shot' }
            }]
          };
        }
      }
    ]);

    // 守卫技能
    this.skills.set('guard', [
      {
        name: 'guard',
        description: '守护一名玩家，使其免受狼人攻击',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && player.isAlive;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          
          // 检查是否连续守护同一人
          if (player.metadata?.lastGuarded === target.playerNumber) {
            return { success: false, message: '不能连续两晚守护同一人', effects: [] };
          }
          
          return {
            success: true,
            message: `守卫守护了${target.playerNumber}号`,
            effects: [{
              type: 'guard',
              target: target.playerNumber,
              source: player.playerNumber
            }]
          };
        }
      }
    ]);

    // 白痴技能
    this.skills.set('idiot', [
      {
        name: 'reveal',
        description: '被投票时翻牌免疫',
        canUse: (player, gameState) => {
          return !player.metadata?.revealed;
        },
        execute: (player, target, gameState) => {
          return {
            success: true,
            message: `${player.playerNumber}号翻牌，是白痴，免疫此次放逐`,
            effects: [{
              type: 'reveal',
              target: player.playerNumber,
              source: player.playerNumber,
              metadata: { role: 'idiot' }
            }]
          };
        }
      }
    ]);

    // 狼王技能
    this.skills.set('wolf_king', [
      {
        name: 'kill',
        description: '夜晚杀死一名玩家',
        canUse: (player, gameState) => {
          return gameState.currentPhase === 'night' && player.isAlive;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          return {
            success: true,
            message: `狼王选择杀死${target.playerNumber}号`,
            effects: [{
              type: 'kill',
              target: target.playerNumber,
              source: player.playerNumber
            }]
          };
        }
      },
      {
        name: 'shoot',
        description: '死后开枪带走一名玩家',
        canUse: (player, gameState) => {
          return !player.isAlive && !player.metadata?.shotUsed;
        },
        execute: (player, target, gameState) => {
          if (!target) {
            return { success: false, message: '必须选择目标', effects: [] };
          }
          return {
            success: true,
            message: `狼王开枪带走了${target.playerNumber}号`,
            effects: [{
              type: 'kill',
              target: target.playerNumber,
              source: player.playerNumber,
              metadata: { reason: 'wolf_king_shot' }
            }]
          };
        }
      }
    ]);
  }

  // 获取角色技能
  getRoleSkills(role: Role): RoleSkill[] {
    return this.skills.get(role) || [];
  }

  // 检查玩家是否可以使用技能
  canUseSkill(player: Player, skillName: string, gameState: any): boolean {
    if (!player.role) return false;
    
    const skills = this.getRoleSkills(player.role);
    const skill = skills.find(s => s.name === skillName);
    
    return skill ? skill.canUse(player, gameState) : false;
  }

  // 执行技能
  executeSkill(
    player: Player, 
    skillName: string, 
    target: Player | null, 
    gameState: any
  ): RoleSkillResult {
    if (!player.role) {
      return { success: false, message: '玩家没有角色', effects: [] };
    }

    const skills = this.getRoleSkills(player.role);
    const skill = skills.find(s => s.name === skillName);
    
    if (!skill) {
      return { success: false, message: '技能不存在', effects: [] };
    }

    if (!skill.canUse(player, gameState)) {
      return { success: false, message: '无法使用此技能', effects: [] };
    }

    return skill.execute(player, target, gameState);
  }

  // 处理夜晚行动
  processNightActions(actions: NightAction[], gameState: any): RoleEffect[] {
    const effects: RoleEffect[] = [];
    const sortedActions = this.sortNightActions(actions);

    for (const action of sortedActions) {
      const player = gameState.players.find((p: Player) => p.playerNumber === action.playerNumber);
      const target = action.target ? 
        gameState.players.find((p: Player) => p.playerNumber === action.target) : null;

      if (player && player.role) {
        const result = this.executeSkill(player, action.skillName, target, gameState);
        if (result.success) {
          effects.push(...result.effects);
        }
      }
    }

    return this.resolveEffects(effects);
  }

  // 按夜晚行动顺序排序
  private sortNightActions(actions: NightAction[]): NightAction[] {
    return actions.sort((a, b) => {
      const orderA = NIGHT_ACTION_ORDER.indexOf(a.role);
      const orderB = NIGHT_ACTION_ORDER.indexOf(b.role);
      return orderA - orderB;
    });
  }

  // 解决效果冲突
  private resolveEffects(effects: RoleEffect[]): RoleEffect[] {
    const resolved: RoleEffect[] = [];
    const targetEffects: { [target: number]: RoleEffect[] } = {};

    // 按目标分组效果
    effects.forEach(effect => {
      if (!targetEffects[effect.target]) {
        targetEffects[effect.target] = [];
      }
      targetEffects[effect.target].push(effect);
    });

    // 解决每个目标的效果冲突
    Object.entries(targetEffects).forEach(([target, effects]) => {
      const targetNum = parseInt(target);
      const killEffect = effects.find(e => e.type === 'kill');
      const saveEffect = effects.find(e => e.type === 'save');
      const guardEffect = effects.find(e => e.type === 'guard');
      const poisonEffect = effects.find(e => e.type === 'poison');

      // 守卫可以阻止狼人杀人，但不能阻止毒杀
      if (killEffect && guardEffect && !poisonEffect) {
        // 守卫成功，不添加杀人效果
        resolved.push(guardEffect);
      } else if (killEffect && saveEffect) {
        // 女巫救人，不添加杀人效果
        resolved.push(saveEffect);
      } else {
        // 添加所有其他效果
        resolved.push(...effects);
      }
    });

    return resolved;
  }

  // 获取角色描述
  getRoleDescription(role: Role): string {
    return ROLE_INFO[role]?.description || '未知角色';
  }

  // 获取角色阵营
  getRoleTeam(role: Role): 'werewolf' | 'villager' | 'third_party' {
    return ROLE_INFO[role]?.team || 'villager';
  }
}
