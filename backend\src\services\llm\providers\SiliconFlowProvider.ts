import axios, { AxiosInstance } from 'axios';
import { LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMError, LLMRateLimitError, LLMAuthError } from '../LLMProvider';

export class SiliconFlowProvider extends LLMProvider {
  private client: AxiosInstance;
  private baseURL = 'https://api.siliconflow.cn/v1';

  constructor(apiKey: string, model: string = 'Qwen/Qwen2.5-7B-Instruct', config: LLMConfig = {}) {
    super(apiKey, model, {
      temperature: 0.8,
      maxTokens: 1000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      ...config
    });

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async sendMessage(messages: LLMMessage[], config?: LLMConfig): Promise<LLMResponse> {
    this.validateMessages(messages);
    const finalConfig = this.mergeConfig(config);

    try {
      return await this.withRetry(async () => {
        this.log('info', `发送消息到硅基流动`, { 
          model: this.model, 
          messageCount: messages.length,
          config: finalConfig 
        });

        const response = await this.client.post('/chat/completions', {
          model: this.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          temperature: finalConfig.temperature,
          max_tokens: finalConfig.maxTokens,
          top_p: finalConfig.topP,
          frequency_penalty: finalConfig.frequencyPenalty,
          presence_penalty: finalConfig.presencePenalty,
          stream: false
        });

        const choice = response.data.choices?.[0];
        if (!choice || !choice.message) {
          throw new LLMError('硅基流动返回了空响应', 'siliconflow', this.model);
        }

        const result: LLMResponse = {
          content: choice.message.content || '',
          usage: response.data.usage ? {
            promptTokens: response.data.usage.prompt_tokens,
            completionTokens: response.data.usage.completion_tokens,
            totalTokens: response.data.usage.total_tokens
          } : undefined,
          model: response.data.model || this.model,
          finishReason: choice.finish_reason || undefined
        };

        this.log('info', `硅基流动响应成功`, { 
          model: this.model,
          usage: result.usage,
          finishReason: result.finishReason
        });

        return result;
      });
    } catch (error: any) {
      this.log('error', `硅基流动调用失败`, { error: error.message });
      
      if (error instanceof LLMError) {
        throw error;
      }

      // 处理硅基流动特定错误
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        switch (status) {
          case 401:
            throw new LLMAuthError('siliconflow', this.model);
          case 429:
            const retryAfter = error.response.headers['retry-after'] ? 
              parseInt(error.response.headers['retry-after']) : undefined;
            throw new LLMRateLimitError('siliconflow', this.model, retryAfter);
          case 400:
            throw new LLMError(`请求参数错误: ${errorData?.error?.message || error.message}`, 'siliconflow', this.model, 'BAD_REQUEST');
          case 404:
            throw new LLMError(`模型不存在: ${this.model}`, 'siliconflow', this.model, 'MODEL_NOT_FOUND');
          case 500:
            throw new LLMError(`服务器内部错误: ${errorData?.error?.message || error.message}`, 'siliconflow', this.model, 'SERVER_ERROR');
          default:
            throw new LLMError(`硅基流动API错误: ${errorData?.error?.message || error.message}`, 'siliconflow', this.model, status.toString());
        }
      }

      this.handleError(error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const testMessages: LLMMessage[] = [
        { role: 'user', content: 'Hello, this is a connection test.' }
      ];

      await this.sendMessage(testMessages, { maxTokens: 10 });
      return true;
    } catch (error) {
      this.log('error', `硅基流动连接测试失败`, { error: (error as Error).message });
      return false;
    }
  }

  getModelInfo() {
    const modelInfo: { [key: string]: any } = {
      'Qwen/Qwen2.5-7B-Instruct': {
        name: 'Qwen2.5-7B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'Qwen/Qwen2.5-14B-Instruct': {
        name: 'Qwen2.5-14B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'Qwen/Qwen2.5-32B-Instruct': {
        name: 'Qwen2.5-32B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'Qwen/Qwen2.5-72B-Instruct': {
        name: 'Qwen2.5-72B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'THUDM/glm-4-9b-chat': {
        name: 'GLM-4-9B-Chat',
        provider: 'siliconflow',
        maxTokens: 8192,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'meta-llama/Meta-Llama-3.1-8B-Instruct': {
        name: 'Llama-3.1-8B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message']
      },
      'meta-llama/Meta-Llama-3.1-70B-Instruct': {
        name: 'Llama-3.1-70B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message']
      },
      'meta-llama/Meta-Llama-3.1-405B-Instruct': {
        name: 'Llama-3.1-405B-Instruct',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message']
      },
      'deepseek-ai/DeepSeek-V2.5': {
        name: 'DeepSeek-V2.5',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      },
      'internlm/internlm2_5-7b-chat': {
        name: 'InternLM2.5-7B-Chat',
        provider: 'siliconflow',
        maxTokens: 32768,
        supportedFeatures: ['chat', 'system_message', 'chinese']
      }
    };

    return modelInfo[this.model] || {
      name: this.model,
      provider: 'siliconflow',
      maxTokens: 32768,
      supportedFeatures: ['chat']
    };
  }

  // 硅基流动特有方法：流式响应
  async sendMessageStream(
    messages: LLMMessage[], 
    config?: LLMConfig,
    onChunk?: (chunk: string) => void
  ): Promise<LLMResponse> {
    this.validateMessages(messages);
    const finalConfig = this.mergeConfig(config);

    try {
      const response = await this.client.post('/chat/completions', {
        model: this.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: finalConfig.temperature,
        max_tokens: finalConfig.maxTokens,
        top_p: finalConfig.topP,
        frequency_penalty: finalConfig.frequencyPenalty,
        presence_penalty: finalConfig.presencePenalty,
        stream: true
      }, {
        responseType: 'stream'
      });

      let fullContent = '';
      let usage: any = undefined;
      let finishReason: string | undefined = undefined;

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk: Buffer) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                resolve({
                  content: fullContent,
                  usage,
                  model: this.model,
                  finishReason
                });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices?.[0]?.delta;
                
                if (delta?.content) {
                  fullContent += delta.content;
                  if (onChunk) {
                    onChunk(delta.content);
                  }
                }

                if (parsed.choices?.[0]?.finish_reason) {
                  finishReason = parsed.choices[0].finish_reason;
                }

                if (parsed.usage) {
                  usage = {
                    promptTokens: parsed.usage.prompt_tokens,
                    completionTokens: parsed.usage.completion_tokens,
                    totalTokens: parsed.usage.total_tokens
                  };
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        });

        response.data.on('error', (error: Error) => {
          reject(error);
        });
      });
    } catch (error) {
      this.log('error', `硅基流动流式调用失败`, { error: (error as Error).message });
      this.handleError(error);
    }
  }

  // 获取可用模型列表
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data
        .map((model: any) => model.id)
        .sort();
    } catch (error) {
      this.log('error', `获取硅基流动模型列表失败`, { error: (error as Error).message });
      return [
        'Qwen/Qwen2.5-7B-Instruct',
        'Qwen/Qwen2.5-14B-Instruct',
        'THUDM/glm-4-9b-chat',
        'meta-llama/Meta-Llama-3.1-8B-Instruct'
      ]; // 返回默认模型列表
    }
  }

  // 计算消息token数量
  estimateMessageTokens(messages: LLMMessage[]): number {
    let totalTokens = 0;
    
    for (const message of messages) {
      // 每条消息的基础开销
      totalTokens += 4;
      
      // 内容token（中文模型token计算可能不同）
      totalTokens += this.estimateTokens(message.content);
      
      // 角色token
      totalTokens += this.estimateTokens(message.role);
    }
    
    // 对话结束标记
    totalTokens += 2;
    
    return totalTokens;
  }
}
