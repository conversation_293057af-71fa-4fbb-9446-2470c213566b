# 🔑 LLM狼人杀游戏 - API密钥配置指南

## 📋 支持的AI模型提供商

### 1. 硅基流动 (SiliconFlow) - 推荐 🌟
**优势**: 国内访问稳定，支持多种开源模型，价格便宜
- **官网**: https://siliconflow.cn
- **文档**: https://docs.siliconflow.cn
- **支持模型**: Qwen2.5、GLM-4、Llama-3.1等

### 2. OpenAI
**优势**: 模型性能优秀，API稳定
- **官网**: https://platform.openai.com
- **支持模型**: GPT-3.5-turbo、GPT-4等

### 3. Anthropic (Claude)
**优势**: 安全性高，长文本处理能力强
- **官网**: https://console.anthropic.com
- **支持模型**: Claude-3-Sonnet、Claude-3-Opus等

### 4. Google (Gemini)
**优势**: 多模态能力强
- **官网**: https://ai.google.dev
- **支持模型**: Gemini-Pro等

## 🚀 快速配置 - 硅基流动 (推荐)

### 1. 注册账号
1. 访问 https://siliconflow.cn
2. 点击"立即注册"
3. 使用手机号或邮箱注册
4. 完成实名认证

### 2. 获取API密钥
1. 登录后进入控制台
2. 点击左侧菜单"API密钥"
3. 点击"创建新密钥"
4. 复制生成的API密钥（格式：sk-xxx）

### 3. 配置到项目
```bash
# 编辑后端环境变量
vi /root/llm/backend/.env

# 找到这一行并替换为你的密钥
SILICONFLOW_API_KEY=sk-your_siliconflow_api_key_here

# 保存后重启后端服务
cd /root/llm
docker-compose restart backend
```

### 4. 测试配置
```bash
# 测试API连接
docker-compose exec backend npm run test:game

# 查看日志确认
docker-compose logs backend | grep -i siliconflow
```

## 🔧 详细配置步骤

### OpenAI配置

**1. 获取API密钥**
1. 访问 https://platform.openai.com
2. 注册/登录账号
3. 进入 API Keys 页面
4. 点击 "Create new secret key"
5. 复制密钥（sk-开头）

**2. 配置到项目**
```bash
vi /root/llm/backend/.env

# 修改这一行
OPENAI_API_KEY=sk-your_openai_api_key_here
```

### Anthropic (Claude) 配置

**1. 获取API密钥**
1. 访问 https://console.anthropic.com
2. 注册/登录账号
3. 进入 API Keys 页面
4. 点击 "Create Key"
5. 复制密钥（sk-ant-开头）

**2. 配置到项目**
```bash
vi /root/llm/backend/.env

# 修改这一行
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here
```

### Google (Gemini) 配置

**1. 获取API密钥**
1. 访问 https://ai.google.dev
2. 点击 "Get API key"
3. 登录Google账号
4. 创建新项目或选择现有项目
5. 生成API密钥

**2. 配置到项目**
```bash
vi /root/llm/backend/.env

# 修改这一行
GOOGLE_API_KEY=your_google_api_key_here
```

## 💰 费用说明

### 硅基流动价格 (人民币)
- **Qwen2.5-7B**: ¥0.7/百万tokens
- **GLM-4-9B**: ¥1.0/百万tokens
- **Llama-3.1-8B**: ¥0.7/百万tokens

### OpenAI价格 (美元)
- **GPT-3.5-turbo**: $0.5/百万tokens (输入) + $1.5/百万tokens (输出)
- **GPT-4**: $30/百万tokens (输入) + $60/百万tokens (输出)

### 预估游戏成本
一局12人狼人杀游戏大约消耗：
- **硅基流动**: ¥0.01-0.05 (推荐)
- **OpenAI GPT-3.5**: $0.01-0.03
- **OpenAI GPT-4**: $0.5-1.0

## 🔒 安全建议

### 1. API密钥安全
```bash
# 设置文件权限
chmod 600 /root/llm/backend/.env

# 定期轮换密钥
# 建议每3个月更换一次API密钥
```

### 2. 使用限制
```bash
# 在API提供商控制台设置：
# - 每月使用限额
# - IP白名单
# - 使用监控告警
```

### 3. 备份配置
```bash
# 备份环境变量文件
cp /root/llm/backend/.env /root/backup/env_backup_$(date +%Y%m%d).txt
```

## 📊 监控使用量

### 1. 查看API调用日志
```bash
# 查看LLM调用日志
docker-compose logs backend | grep -i "LLM调用"

# 查看错误日志
docker-compose logs backend | grep -i "API.*error"
```

### 2. 创建使用量监控脚本
```bash
cat > /root/api_usage_monitor.sh << 'EOF'
#!/bin/bash
echo "=== API使用量监控 $(date) ==="

# 统计今日API调用次数
TODAY=$(date +%Y-%m-%d)
echo "今日API调用统计:"
docker-compose logs backend | grep "$TODAY" | grep -c "LLM调用成功" || echo "0"

# 检查API密钥状态
echo -e "\nAPI密钥状态检查:"
docker-compose exec backend npm run test:api 2>/dev/null || echo "API测试失败"

echo "========================"
EOF

chmod +x /root/api_usage_monitor.sh
```

### 3. 设置告警
```bash
# 添加到定时任务
crontab -e

# 每小时检查一次
0 * * * * /root/api_usage_monitor.sh >> /var/log/api_usage.log 2>&1
```

## 🎮 游戏中的模型选择

### 创建游戏时的模型配置
在前端创建游戏时，可以选择以下模型组合：

**推荐配置1 - 硅基流动混合**:
- 4个 Qwen2.5-7B (快速响应)
- 4个 GLM-4-9B (中等性能)
- 4个 Qwen2.5-14B (高性能)

**推荐配置2 - 性能对比**:
- 3个 GPT-3.5-turbo
- 3个 Claude-3-Sonnet
- 3个 Qwen2.5-7B
- 3个 GLM-4-9B

**推荐配置3 - 经济实用**:
- 12个 Qwen2.5-7B (最经济)

## 🔄 配置更新流程

### 1. 添加新的API密钥
```bash
# 编辑配置文件
vi /root/llm/backend/.env

# 添加或修改API密钥
SILICONFLOW_API_KEY=sk-new_key_here

# 重启后端服务
docker-compose restart backend

# 验证配置
docker-compose logs backend | tail -20
```

### 2. 测试新配置
```bash
# 运行测试脚本
docker-compose exec backend npm run test:game

# 创建测试游戏验证
curl -X POST http://localhost:8000/api/games \
  -H "Content-Type: application/json" \
  -d '{"name":"测试游戏","gameMode":"standard_12","aiModels":["Qwen/Qwen2.5-7B-Instruct"]}'
```

## ❓ 常见问题

### Q: 哪个API提供商最推荐？
A: 推荐硅基流动，国内访问稳定，价格便宜，模型性能不错。

### Q: 可以只配置一个API密钥吗？
A: 可以，至少配置一个即可。建议配置多个以提供更多模型选择。

### Q: API密钥配置错误怎么办？
A: 检查密钥格式，确保没有多余空格，重启backend服务后查看日志。

### Q: 如何控制API使用成本？
A: 在API提供商控制台设置使用限额，选择较便宜的模型如Qwen2.5-7B。

### Q: API调用失败怎么办？
A: 检查网络连接、API密钥有效性、账户余额，查看详细错误日志。

---

**提示**: 建议先配置硅基流动API密钥进行测试，稳定后再添加其他提供商的密钥。
