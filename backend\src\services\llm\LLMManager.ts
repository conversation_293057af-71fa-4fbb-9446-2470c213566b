import { LLMProvider, LLMProviderFactory, LLMMessage, LLMResponse, LLMConfig } from './LLMProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { ClaudeProvider } from './providers/ClaudeProvider';
import { SiliconFlowProvider } from './providers/SiliconFlowProvider';
import { prisma, cache, cacheKeys } from '../database';
import { AIProvider } from '../../../shared/types/game';

// 注册所有提供商
LLMProviderFactory.registerProvider('openai', OpenAIProvider);
LLMProviderFactory.registerProvider('anthropic', ClaudeProvider);
LLMProviderFactory.registerProvider('siliconflow', SiliconFlowProvider);

export interface LLMManagerConfig {
  defaultProvider?: string;
  defaultModel?: string;
  enableCache?: boolean;
  cacheTimeout?: number;
  maxRetries?: number;
  timeout?: number;
}

export interface ModelPerformance {
  provider: string;
  model: string;
  totalCalls: number;
  successCalls: number;
  failedCalls: number;
  avgResponseTime: number;
  lastUsed: Date;
}

export class LLMManager {
  private static instance: LLMManager;
  private providers: Map<string, LLMProvider> = new Map();
  private config: LLMManagerConfig;
  private performanceStats: Map<string, ModelPerformance> = new Map();

  private constructor(config: LLMManagerConfig = {}) {
    this.config = {
      defaultProvider: 'openai',
      defaultModel: 'gpt-3.5-turbo',
      enableCache: true,
      cacheTimeout: 300, // 5分钟
      maxRetries: 3,
      timeout: 30000,
      ...config
    };
  }

  static getInstance(config?: LLMManagerConfig): LLMManager {
    if (!LLMManager.instance) {
      LLMManager.instance = new LLMManager(config);
    }
    return LLMManager.instance;
  }

  // 初始化提供商
  async initializeProvider(
    provider: AIProvider,
    apiKey: string,
    model?: string,
    config?: LLMConfig
  ): Promise<boolean> {
    try {
      const providerKey = `${provider}-${model || 'default'}`;
      
      // 创建提供商实例
      const providerInstance = LLMProviderFactory.createProvider(
        provider,
        apiKey,
        model || this.getDefaultModel(provider),
        config
      );

      // 测试连接
      const isConnected = await providerInstance.testConnection();
      if (!isConnected) {
        throw new Error(`${provider} 连接测试失败`);
      }

      // 存储提供商实例
      this.providers.set(providerKey, providerInstance);
      
      // 初始化性能统计
      this.performanceStats.set(providerKey, {
        provider,
        model: model || this.getDefaultModel(provider),
        totalCalls: 0,
        successCalls: 0,
        failedCalls: 0,
        avgResponseTime: 0,
        lastUsed: new Date()
      });

      console.log(`✅ LLM提供商初始化成功: ${provider} (${model})`);
      return true;
    } catch (error) {
      console.error(`❌ LLM提供商初始化失败: ${provider}`, error);
      return false;
    }
  }

  // 发送消息
  async sendMessage(
    messages: LLMMessage[],
    provider?: AIProvider,
    model?: string,
    config?: LLMConfig
  ): Promise<LLMResponse> {
    const providerName = provider || this.config.defaultProvider!;
    const modelName = model || this.getDefaultModel(providerName);
    const providerKey = `${providerName}-${modelName}`;

    // 检查缓存
    if (this.config.enableCache) {
      const cacheKey = this.generateCacheKey(messages, providerKey, config);
      const cachedResponse = await cache.get<LLMResponse>(cacheKey);
      if (cachedResponse) {
        console.log(`📦 使用缓存响应: ${providerKey}`);
        return cachedResponse;
      }
    }

    // 获取提供商实例
    const providerInstance = this.providers.get(providerKey);
    if (!providerInstance) {
      throw new Error(`提供商未初始化: ${providerKey}`);
    }

    // 记录开始时间
    const startTime = Date.now();
    
    try {
      // 发送消息
      const response = await providerInstance.sendMessage(messages, config);
      
      // 记录性能统计
      const responseTime = Date.now() - startTime;
      this.updatePerformanceStats(providerKey, true, responseTime);

      // 缓存响应
      if (this.config.enableCache) {
        const cacheKey = this.generateCacheKey(messages, providerKey, config);
        await cache.set(cacheKey, response, this.config.cacheTimeout!);
      }

      console.log(`✅ LLM调用成功: ${providerKey} (${responseTime}ms)`);
      return response;
    } catch (error) {
      // 记录失败统计
      const responseTime = Date.now() - startTime;
      this.updatePerformanceStats(providerKey, false, responseTime);
      
      console.error(`❌ LLM调用失败: ${providerKey}`, error);
      throw error;
    }
  }

  // 批量发送消息
  async sendMessagesBatch(
    requests: Array<{
      messages: LLMMessage[];
      provider?: AIProvider;
      model?: string;
      config?: LLMConfig;
    }>
  ): Promise<LLMResponse[]> {
    const promises = requests.map(request =>
      this.sendMessage(
        request.messages,
        request.provider,
        request.model,
        request.config
      )
    );

    return Promise.all(promises);
  }

  // 获取可用提供商
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  // 获取提供商信息
  getProviderInfo(provider: AIProvider, model?: string): any {
    const providerKey = `${provider}-${model || this.getDefaultModel(provider)}`;
    const providerInstance = this.providers.get(providerKey);
    
    if (!providerInstance) {
      return null;
    }

    return providerInstance.getModelInfo();
  }

  // 获取性能统计
  getPerformanceStats(provider?: AIProvider, model?: string): ModelPerformance[] {
    if (provider && model) {
      const providerKey = `${provider}-${model}`;
      const stats = this.performanceStats.get(providerKey);
      return stats ? [stats] : [];
    }

    return Array.from(this.performanceStats.values());
  }

  // 测试所有提供商连接
  async testAllConnections(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};

    for (const [providerKey, provider] of this.providers) {
      try {
        results[providerKey] = await provider.testConnection();
      } catch (error) {
        results[providerKey] = false;
      }
    }

    return results;
  }

  // 从数据库加载用户API密钥
  async loadUserApiKeys(userId: string): Promise<void> {
    try {
      const apiKeys = await prisma.userApiKey.findMany({
        where: {
          userId,
          isActive: true
        }
      });

      for (const apiKey of apiKeys) {
        // 解密API密钥（这里简化处理，实际应该使用加密）
        const decryptedKey = apiKey.apiKeyEncrypted; // TODO: 实现解密
        
        // 获取该提供商的默认模型
        const models = await this.getProviderModels(apiKey.provider as AIProvider);
        
        for (const model of models) {
          await this.initializeProvider(
            apiKey.provider as AIProvider,
            decryptedKey,
            model
          );
        }
      }
    } catch (error) {
      console.error('加载用户API密钥失败:', error);
    }
  }

  // 获取提供商支持的模型列表
  private async getProviderModels(provider: AIProvider): Promise<string[]> {
    const defaultModels: { [key in AIProvider]: string[] } = {
      openai: ['gpt-3.5-turbo', 'gpt-4'],
      anthropic: ['claude-3-sonnet-20240229', 'claude-3-opus-20240229'],
      google: ['gemini-pro'],
      siliconflow: [
        'Qwen/Qwen2.5-7B-Instruct',
        'Qwen/Qwen2.5-14B-Instruct',
        'THUDM/glm-4-9b-chat',
        'meta-llama/Meta-Llama-3.1-8B-Instruct'
      ],
      qwen: ['qwen-turbo'],
      ernie: ['ernie-bot'],
      spark: ['spark-3.0']
    };

    return defaultModels[provider] || [];
  }

  // 获取默认模型
  private getDefaultModel(provider: string): string {
    const defaultModels: { [key: string]: string } = {
      openai: 'gpt-3.5-turbo',
      anthropic: 'claude-3-sonnet-20240229',
      google: 'gemini-pro',
      siliconflow: 'Qwen/Qwen2.5-7B-Instruct',
      qwen: 'qwen-turbo',
      ernie: 'ernie-bot',
      spark: 'spark-3.0'
    };

    return defaultModels[provider] || 'gpt-3.5-turbo';
  }

  // 生成缓存键
  private generateCacheKey(
    messages: LLMMessage[],
    providerKey: string,
    config?: LLMConfig
  ): string {
    const messageHash = Buffer.from(JSON.stringify(messages)).toString('base64');
    const configHash = config ? Buffer.from(JSON.stringify(config)).toString('base64') : '';
    return `llm:${providerKey}:${messageHash}:${configHash}`;
  }

  // 更新性能统计
  private updatePerformanceStats(
    providerKey: string,
    success: boolean,
    responseTime: number
  ): void {
    const stats = this.performanceStats.get(providerKey);
    if (!stats) return;

    stats.totalCalls++;
    if (success) {
      stats.successCalls++;
    } else {
      stats.failedCalls++;
    }

    // 更新平均响应时间
    stats.avgResponseTime = (stats.avgResponseTime * (stats.totalCalls - 1) + responseTime) / stats.totalCalls;
    stats.lastUsed = new Date();

    this.performanceStats.set(providerKey, stats);
  }

  // 清理资源
  async cleanup(): Promise<void> {
    this.providers.clear();
    this.performanceStats.clear();
    console.log('✅ LLM管理器已清理');
  }
}
