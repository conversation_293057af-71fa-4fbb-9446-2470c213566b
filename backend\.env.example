# 服务器配置
NODE_ENV=development
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://werewolf_user:werewolf_password@localhost:5432/werewolf_db

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=http://localhost:3000

# LLM API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key

# 国产模型API Keys (可选)
QWEN_API_KEY=your_qwen_api_key
ERNIE_API_KEY=your_ernie_api_key
SPARK_API_KEY=your_spark_api_key

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 游戏配置
DEFAULT_GAME_MODE=standard_12
MAX_GAMES_PER_USER=5
GAME_TIMEOUT_MINUTES=60

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
