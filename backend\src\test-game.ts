// 简单的游戏测试脚本
import { GameManager } from './services/game/GameManager';
import { LLMManager } from './services/llm/LLMManager';
import { DecisionEngine } from './services/ai/DecisionEngine';
import { initializeDatabase } from './services/database';

async function testGameSystem() {
  console.log('🧪 开始测试游戏系统...');

  try {
    // 初始化数据库
    console.log('📊 初始化数据库...');
    await initializeDatabase();

    // 初始化管理器
    const gameManager = GameManager.getInstance();
    const llmManager = LLMManager.getInstance();
    const decisionEngine = new DecisionEngine();

    // 创建测试游戏
    console.log('🎮 创建测试游戏...');
    const gameState = await gameManager.createGame('测试游戏', 'standard_12');
    console.log(`✅ 游戏创建成功: ${gameState.id}`);

    // 添加AI玩家
    console.log('🤖 添加AI玩家...');
    const aiModels = [
      'gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet-20240229',
      'gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet-20240229',
      'gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet-20240229',
      'gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet-20240229'
    ];

    for (let i = 0; i < 12; i++) {
      const player = await gameManager.joinGame(
        gameState.id,
        'ai',
        aiModels[i]
      );
      console.log(`👤 玩家${player.playerNumber}号加入 (${aiModels[i]})`);

      // 初始化AI决策引擎
      if (player.role) {
        decisionEngine.initializeAIPlayer(player);
      }
    }

    // 测试开始游戏
    console.log('🚀 开始游戏...');
    const started = await gameManager.startGame(gameState.id);
    if (started) {
      console.log('✅ 游戏开始成功');
      
      // 获取更新后的游戏状态
      const updatedGameState = await gameManager.getGameState(gameState.id);
      if (updatedGameState) {
        console.log(`🎯 游戏状态: ${updatedGameState.status}`);
        console.log(`🌙 当前阶段: ${updatedGameState.currentPhase}`);
        console.log(`🔄 当前回合: ${updatedGameState.currentRound}`);
        
        // 显示角色分配
        console.log('\n👥 角色分配:');
        updatedGameState.players.forEach(player => {
          console.log(`${player.playerNumber}号: ${player.role} (${player.aiModel})`);
        });
      }
    } else {
      console.log('❌ 游戏开始失败');
    }

    // 测试AI决策（如果有API密钥的话）
    console.log('\n🧠 测试AI决策系统...');
    try {
      // 这里需要真实的API密钥才能测试
      // const testPlayer = updatedGameState?.players[0];
      // if (testPlayer) {
      //   const decision = await decisionEngine.makeDecision(
      //     testPlayer.id,
      //     updatedGameState!,
      //     'day',
      //     'speak'
      //   );
      //   console.log('🎯 AI决策结果:', decision);
      // }
      console.log('⚠️  跳过AI决策测试（需要API密钥）');
    } catch (error) {
      console.log('⚠️  AI决策测试失败（可能缺少API密钥）:', (error as Error).message);
    }

    console.log('\n✅ 游戏系统测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testGameSystem().then(() => {
    console.log('🏁 测试结束');
    process.exit(0);
  }).catch(error => {
    console.error('💥 测试异常:', error);
    process.exit(1);
  });
}

export { testGameSystem };
