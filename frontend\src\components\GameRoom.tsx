'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { useGameSocket } from '@/hooks/useSocket';

interface Player {
  id: string;
  playerNumber: number;
  playerType: 'ai' | 'human';
  aiModel?: string;
  role?: string;
  isAlive: boolean;
  isCaptain: boolean;
  votes: number;
  hasVoted: boolean;
}

interface GameState {
  id: string;
  name: string;
  status: 'waiting' | 'playing' | 'finished';
  currentPhase: 'preparation' | 'night' | 'day' | 'vote' | 'finished';
  currentRound: number;
  timeRemaining: number;
  players: Player[];
  winner?: string;
}

interface GameMessage {
  id: string;
  type: 'system' | 'speak' | 'vote' | 'skill';
  playerNumber?: number;
  content: string;
  timestamp: string;
}

interface GameRoomProps {
  gameId: string;
  onLeaveGame: () => void;
}

export const GameRoom: React.FC<GameRoomProps> = ({
  gameId,
  onLeaveGame
}) => {
  const [loading, setLoading] = useState(true);
  const {
    gameState,
    messages,
    connected,
    connecting,
    error,
    joinGame,
    leaveGame
  } = useGameSocket(gameId);

  useEffect(() => {
    fetchGameState();
    if (connected && gameId) {
      joinGame(gameId);
    }
  }, [gameId, connected]);

  const fetchGameState = async () => {
    try {
      const response = await fetch(`/api/games/${gameId}`);
      const data = await response.json();

      if (data.success && !gameState) {
        // 只在WebSocket还没有提供游戏状态时使用HTTP获取的状态
        // setGameState(data.data.game);
      }
    } catch (error) {
      console.error('获取游戏状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveGame = () => {
    if (gameId) {
      leaveGame(gameId);
    }
    onLeaveGame();
  };

  const getPhaseText = (phase: string) => {
    const phaseMap = {
      preparation: '准备阶段',
      night: '夜晚阶段',
      day: '白天讨论',
      vote: '投票阶段',
      finished: '游戏结束'
    };
    return phaseMap[phase as keyof typeof phaseMap] || phase;
  };

  const getRoleText = (role?: string) => {
    const roleMap = {
      werewolf: '狼人',
      villager: '村民',
      prophet: '预言家',
      witch: '女巫',
      hunter: '猎人',
      idiot: '白痴',
      guard: '守卫',
      wolf_king: '狼王'
    };
    return role ? roleMap[role as keyof typeof roleMap] || role : '未知';
  };

  const getPlayerStatusColor = (player: Player) => {
    if (!player.isAlive) return 'border-red-500/50 bg-red-900/20';
    if (player.isCaptain) return 'border-yellow-500/50 bg-yellow-900/20';
    return 'border-blue-500/50 bg-blue-900/20';
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading || connecting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white mb-4"></div>
          <p className="text-white">
            {connecting ? '连接游戏中...' : '加载游戏中...'}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <Card>
          <CardContent className="text-center py-8">
            <h2 className="text-xl font-semibold text-white mb-4">连接错误</h2>
            <p className="text-gray-300 mb-6">{error}</p>
            <Button onClick={handleLeaveGame}>返回大厅</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <Card>
          <CardContent className="text-center py-8">
            <h2 className="text-xl font-semibold text-white mb-4">游戏不存在</h2>
            <Button onClick={handleLeaveGame}>返回大厅</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-4">
      <div className="max-w-7xl mx-auto">
        {/* 游戏头部信息 */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <CardTitle className="text-2xl">{gameState.name}</CardTitle>
                <div className="flex items-center gap-4 mt-2 text-sm text-gray-300">
                  <span>第 {gameState.currentRound} 轮</span>
                  <span className="px-2 py-1 bg-purple-600/30 rounded">
                    {getPhaseText(gameState.currentPhase)}
                  </span>
                  {gameState.timeRemaining > 0 && (
                    <span className="px-2 py-1 bg-orange-600/30 rounded">
                      ⏱️ {formatTime(gameState.timeRemaining)}
                    </span>
                  )}
                </div>
              </div>
              <Button variant="ghost" onClick={handleLeaveGame}>
                返回大厅
              </Button>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 玩家列表 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>玩家列表 ({gameState.players.filter(p => p.isAlive).length}/12 存活)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                  {gameState.players.map((player) => (
                    <div
                      key={player.id}
                      className={`p-3 rounded-lg border-2 transition-all ${getPlayerStatusColor(player)}`}
                    >
                      <div className="text-center">
                        <div className="text-lg font-bold text-white mb-1">
                          {player.playerNumber}号
                          {player.isCaptain && ' 👑'}
                        </div>
                        
                        {gameState.status === 'finished' && player.role && (
                          <div className="text-sm text-gray-300 mb-1">
                            {getRoleText(player.role)}
                          </div>
                        )}
                        
                        <div className="text-xs text-gray-400 truncate">
                          {player.aiModel?.split('/').pop() || player.aiModel}
                        </div>
                        
                        <div className={`text-xs mt-1 ${player.isAlive ? 'text-green-400' : 'text-red-400'}`}>
                          {player.isAlive ? '存活' : '死亡'}
                        </div>

                        {gameState.currentPhase === 'vote' && player.isAlive && (
                          <div className="text-xs mt-1 text-yellow-400">
                            票数: {player.votes}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 游戏日志 */}
          <div>
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle>游戏日志</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto space-y-2">
                  {messages.length === 0 ? (
                    <div className="text-center text-gray-400 py-8">
                      <div className="text-4xl mb-2">📝</div>
                      <p>游戏日志将在这里显示</p>
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`p-3 rounded-lg ${
                          message.type === 'system' 
                            ? 'bg-gray-700/50' 
                            : 'bg-white/5'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <span className="text-sm font-medium text-white">
                            {message.type === 'system' 
                              ? '系统' 
                              : `${message.playerNumber}号`
                            }
                          </span>
                          <span className="text-xs text-gray-400">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-gray-300 text-sm">{message.content}</p>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 游戏结果 */}
        {gameState.status === 'finished' && (
          <Card className="mt-6">
            <CardContent className="text-center py-8">
              <div className="text-6xl mb-4">
                {gameState.winner === 'werewolf' ? '🐺' : '👥'}
              </div>
              <h2 className="text-3xl font-bold text-white mb-2">
                {gameState.winner === 'werewolf' ? '狼人获胜！' : '好人获胜！'}
              </h2>
              <p className="text-gray-300 mb-6">
                游戏已结束，感谢观战！
              </p>
              <Button onClick={handleLeaveGame}>
                返回大厅
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
