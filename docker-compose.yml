version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: werewolf-postgres
    environment:
      POSTGRES_DB: werewolf_db
      POSTGRES_USER: werewolf_user
      POSTGRES_PASSWORD: werewolf_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - werewolf-network
    restart: unless-stopped

  # Redis (用于会话管理和缓存)
  redis:
    image: redis:7-alpine
    container_name: werewolf-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - werewolf-network
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: werewolf-backend
    environment:
      - NODE_ENV=development
      - PORT=8000
      - DATABASE_URL=**********************************************************/werewolf_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - CORS_ORIGIN=http://localhost:3000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - werewolf-network
    restart: unless-stopped
    command: npm run dev

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: werewolf-frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - werewolf-network
    restart: unless-stopped
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  werewolf-network:
    driver: bridge
