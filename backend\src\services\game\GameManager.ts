import { GameEngine } from './GameEngine';
import { GameState, Player, GameAction } from '../../../shared/types/game';
import { prisma, cache, cacheKeys } from '../database';
import { v4 as uuidv4 } from 'uuid';

export class GameManager {
  private static instance: GameManager;
  private games: Map<string, GameEngine> = new Map();

  private constructor() {}

  static getInstance(): GameManager {
    if (!GameManager.instance) {
      GameManager.instance = new GameManager();
    }
    return GameManager.instance;
  }

  // 创建游戏
  async createGame(
    name: string, 
    gameMode: string = 'standard_12', 
    createdBy?: string
  ): Promise<GameState> {
    const gameId = uuidv4();
    
    // 创建游戏引擎实例
    const gameEngine = new GameEngine(gameId, name, gameMode);
    
    // 设置事件监听
    this.setupGameEventListeners(gameEngine);
    
    // 存储游戏实例
    this.games.set(gameId, gameEngine);
    
    // 保存到数据库
    await prisma.game.create({
      data: {
        id: gameId,
        name,
        gameMode,
        createdBy,
        gameData: gameEngine.getGameState()
      }
    });

    // 缓存游戏状态
    await cache.set(cacheKeys.game(gameId), gameEngine.getGameState(), 3600);
    
    const gameState = gameEngine.getGameState();
    console.log(`🎮 游戏创建成功: ${name} (${gameId})`);
    
    return gameState;
  }

  // 获取游戏
  getGame(gameId: string): GameEngine | null {
    return this.games.get(gameId) || null;
  }

  // 获取游戏状态
  async getGameState(gameId: string): Promise<GameState | null> {
    // 先从内存中获取
    const game = this.games.get(gameId);
    if (game) {
      return game.getGameState();
    }

    // 从缓存中获取
    const cachedState = await cache.get<GameState>(cacheKeys.game(gameId));
    if (cachedState) {
      return cachedState;
    }

    // 从数据库中获取
    const dbGame = await prisma.game.findUnique({
      where: { id: gameId },
      include: {
        players: true
      }
    });

    if (dbGame && dbGame.gameData) {
      return dbGame.gameData as GameState;
    }

    return null;
  }

  // 加入游戏
  async joinGame(
    gameId: string, 
    playerType: 'ai' | 'human', 
    aiModel?: string
  ): Promise<Player> {
    const game = this.games.get(gameId);
    if (!game) {
      throw new Error('游戏不存在');
    }

    const player = game.addPlayer(playerType, aiModel);
    if (!player) {
      throw new Error('加入游戏失败');
    }

    // 保存玩家到数据库
    await prisma.gamePlayer.create({
      data: {
        id: player.id,
        gameId,
        playerNumber: player.playerNumber,
        playerType,
        aiModel,
        isAlive: true,
        isCaptain: false
      }
    });

    // 更新游戏状态缓存
    await cache.set(cacheKeys.game(gameId), game.getGameState(), 3600);
    
    console.log(`👤 玩家加入游戏: ${player.playerNumber}号 (${gameId})`);
    
    return player;
  }

  // 开始游戏
  async startGame(gameId: string): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game) {
      throw new Error('游戏不存在');
    }

    const success = game.startGame();
    
    if (success) {
      // 更新数据库
      await prisma.game.update({
        where: { id: gameId },
        data: {
          status: 'playing',
          startedAt: new Date(),
          gameData: game.getGameState()
        }
      });

      // 更新缓存
      await cache.set(cacheKeys.game(gameId), game.getGameState(), 3600);
      
      console.log(`🚀 游戏开始: ${gameId}`);
    }

    return success;
  }

  // 执行游戏行动
  async executeAction(gameId: string, action: GameAction): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game) {
      throw new Error('游戏不存在');
    }

    const success = game.executeAction(action);
    
    if (success) {
      // 记录行动到数据库
      await prisma.gameLog.create({
        data: {
          id: action.id,
          gameId,
          phase: action.phase,
          roundNumber: action.round,
          actionType: action.actionType,
          playerNumber: action.playerNumber,
          targetPlayer: action.targetPlayer,
          content: action.content,
          metadata: action.metadata
        }
      });

      // 更新游戏状态缓存
      await cache.set(cacheKeys.game(gameId), game.getGameState(), 3600);
    }

    return success;
  }

  // 获取游戏列表
  async getGameList(
    status?: string, 
    page: number = 1, 
    limit: number = 10
  ): Promise<{
    games: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const where = status ? { status } : {};
    const skip = (page - 1) * limit;

    const [games, total] = await Promise.all([
      prisma.game.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { players: true }
          }
        }
      }),
      prisma.game.count({ where })
    ]);

    return {
      games: games.map(game => ({
        id: game.id,
        name: game.name,
        status: game.status,
        gameMode: game.gameMode,
        currentPlayers: game._count.players,
        maxPlayers: game.maxPlayers,
        createdAt: game.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // 设置游戏事件监听器
  private setupGameEventListeners(gameEngine: GameEngine): void {
    const gameId = gameEngine.getGameState().id;

    gameEngine.on('playerJoined', async (player: Player) => {
      await cache.set(cacheKeys.game(gameId), gameEngine.getGameState(), 3600);
    });

    gameEngine.on('gameStarted', async (gameState: GameState) => {
      await cache.set(cacheKeys.game(gameId), gameState, 3600);
    });

    gameEngine.on('phaseChanged', async (data: any) => {
      await cache.set(cacheKeys.game(gameId), gameEngine.getGameState(), 3600);
    });

    gameEngine.on('playerEliminated', async (data: any) => {
      await cache.set(cacheKeys.game(gameId), gameEngine.getGameState(), 3600);
    });

    gameEngine.on('gameEnded', async (data: any) => {
      // 更新数据库
      await prisma.game.update({
        where: { id: gameId },
        data: {
          status: 'finished',
          finishedAt: new Date(),
          winnerTeam: data.winner,
          gameData: data.gameState
        }
      });

      // 更新缓存
      await cache.set(cacheKeys.game(gameId), data.gameState, 3600);
      
      // 清理内存中的游戏实例（延迟清理，给客户端时间获取最终状态）
      setTimeout(() => {
        this.cleanupGame(gameId);
      }, 60000); // 1分钟后清理
      
      console.log(`🏁 游戏结束: ${gameId}, 获胜方: ${data.winner}`);
    });
  }

  // 清理游戏
  private cleanupGame(gameId: string): void {
    const game = this.games.get(gameId);
    if (game) {
      game.destroy();
      this.games.delete(gameId);
      console.log(`🧹 游戏实例已清理: ${gameId}`);
    }
  }

  // 获取活跃游戏数量
  getActiveGameCount(): number {
    return this.games.size;
  }

  // 获取所有活跃游戏ID
  getActiveGameIds(): string[] {
    return Array.from(this.games.keys());
  }

  // 强制清理所有游戏（用于服务器关闭时）
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理所有游戏实例...');
    
    for (const [gameId, game] of this.games) {
      game.destroy();
      this.games.delete(gameId);
    }
    
    console.log('✅ 所有游戏实例已清理完成');
  }
}
