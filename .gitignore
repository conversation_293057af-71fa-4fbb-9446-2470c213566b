# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物
dist/
build/
.next/
out/

# 缓存
.cache/
.parcel-cache/
.npm
.eslintcache
.stylelintcache

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 日志
logs
*.log

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 测试
.jest/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 数据库
*.sqlite
*.sqlite3
*.db

# SSL证书
nginx/ssl/
*.pem
*.key
*.crt

# 临时文件
tmp/
temp/

# 备份文件
*.bak
*.backup

# Prisma
prisma/migrations/

# TypeScript
*.tsbuildinfo

# Next.js
.next/
out/

# Vercel
.vercel

# Turbo
.turbo
