import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'LLM狼人杀 - AI模型对战游戏',
  description: '观看不同LLM模型模拟真人玩网易狼人杀游戏，体验人工智能的推理与演技',
  keywords: ['狼人杀', 'LLM', 'AI', '游戏', '人工智能'],
  authors: [{ name: 'LLM狼人杀开发团队' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
