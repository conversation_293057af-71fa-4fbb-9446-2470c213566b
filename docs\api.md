# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8000/api`
- **Content-Type**: `application/json`
- **认证方式**: JWT <PERSON>ken (Bearer)

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求体:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "username": "string",
      "email": "string"
    },
    "token": "jwt_token"
  }
}
```

### 用户登录
```http
POST /auth/login
```

**请求体:**
```json
{
  "username": "string",
  "password": "string"
}
```

## 游戏管理接口

### 获取游戏列表
```http
GET /games?status=waiting&page=1&limit=10
```

**查询参数:**
- `status`: 游戏状态 (waiting, playing, finished)
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)

**响应:**
```json
{
  "success": true,
  "data": {
    "games": [
      {
        "id": "uuid",
        "name": "string",
        "status": "waiting",
        "gameMode": "standard_12",
        "currentPlayers": 0,
        "maxPlayers": 12,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

### 创建游戏
```http
POST /games
```

**请求体:**
```json
{
  "name": "string",
  "gameMode": "standard_12",
  "aiModels": ["gpt-4", "claude-3-opus"]
}
```

### 获取游戏详情
```http
GET /games/:gameId
```

### 加入游戏
```http
POST /games/:gameId/join
```

**请求体:**
```json
{
  "playerType": "ai",
  "aiModel": "gpt-4"
}
```

### 开始游戏
```http
POST /games/:gameId/start
```

### 游戏操作
```http
POST /games/:gameId/action
```

**请求体:**
```json
{
  "actionType": "speak|vote|skill_use",
  "targetPlayer": 1,
  "content": "string",
  "metadata": {}
}
```

## AI模型管理接口

### 获取可用模型列表
```http
GET /models
```

**响应:**
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "uuid",
        "name": "gpt-4",
        "displayName": "GPT-4",
        "provider": "openai",
        "isActive": true
      }
    ]
  }
}
```

### 配置用户API密钥
```http
POST /models/api-keys
```

**请求体:**
```json
{
  "provider": "openai",
  "apiKey": "string"
}
```

### 获取用户API密钥状态
```http
GET /models/api-keys
```

## 用户管理接口

### 获取用户信息
```http
GET /users/profile
```

### 更新用户信息
```http
PUT /users/profile
```

### 获取用户游戏历史
```http
GET /users/games?page=1&limit=10
```

## 统计接口

### 获取游戏统计
```http
GET /stats/games
```

### 获取模型性能统计
```http
GET /stats/models
```

## WebSocket事件

### 连接
```javascript
const socket = io('http://localhost:8000');
```

### 游戏事件

#### 加入游戏房间
```javascript
socket.emit('game:join', { gameId: 'uuid' });
```

#### 游戏状态更新
```javascript
socket.on('game:state_update', (gameState) => {
  // 处理游戏状态更新
});
```

#### 玩家发言
```javascript
socket.on('player:speak', (data) => {
  // data: { playerNumber, content, timestamp }
});
```

#### 玩家投票
```javascript
socket.on('player:vote', (data) => {
  // data: { playerNumber, targetPlayer, timestamp }
});
```

#### 阶段变化
```javascript
socket.on('game:phase_change', (data) => {
  // data: { phase, timeRemaining, round }
});
```

#### 游戏结束
```javascript
socket.on('game:end', (data) => {
  // data: { winner, gameState, statistics }
});
```

### 系统事件

#### 错误处理
```javascript
socket.on('error', (error) => {
  // 处理错误
});
```

#### 连接状态
```javascript
socket.on('connect', () => {
  console.log('已连接到服务器');
});

socket.on('disconnect', () => {
  console.log('与服务器断开连接');
});
```

## 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  }
}
```

### 常见错误码

- `INVALID_REQUEST`: 请求参数无效
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `GAME_FULL`: 游戏人数已满
- `GAME_ALREADY_STARTED`: 游戏已开始
- `INVALID_ACTION`: 无效操作
- `API_KEY_REQUIRED`: 需要API密钥
- `MODEL_NOT_AVAILABLE`: 模型不可用

## 状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 未找到
- `409`: 冲突
- `500`: 服务器错误
