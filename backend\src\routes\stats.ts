import { Router } from 'express';

const router = Router();

// 获取游戏统计
router.get('/games', async (req, res) => {
  try {
    // TODO: 实现游戏统计逻辑
    
    res.json({
      success: true,
      data: {
        total: 0,
        active: 0,
        finished: 0,
        byMode: {
          standard_12: 0,
          advanced_12: 0,
          custom: 0
        },
        byStatus: {
          waiting: 0,
          playing: 0,
          finished: 0
        }
      }
    });
  } catch (error) {
    console.error('获取游戏统计失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_GAME_STATS_FAILED',
        message: '获取游戏统计失败'
      }
    });
  }
});

// 获取模型性能统计
router.get('/models', async (req, res) => {
  try {
    // TODO: 实现模型性能统计逻辑
    
    res.json({
      success: true,
      data: {
        models: [
          {
            name: 'gpt-4',
            gamesPlayed: 0,
            winRate: 0,
            avgResponseTime: 0,
            performance: {
              werewolf: { games: 0, wins: 0 },
              villager: { games: 0, wins: 0 }
            }
          }
        ]
      }
    });
  } catch (error) {
    console.error('获取模型统计失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_MODEL_STATS_FAILED',
        message: '获取模型统计失败'
      }
    });
  }
});

export default router;
