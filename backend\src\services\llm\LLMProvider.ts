// LLM提供商统一接口

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason?: string;
}

export interface LLMConfig {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  [key: string]: any;
}

export abstract class LLMProvider {
  protected apiKey: string;
  protected model: string;
  protected baseConfig: LLMConfig;

  constructor(apiKey: string, model: string, config: LLMConfig = {}) {
    this.apiKey = apiKey;
    this.model = model;
    this.baseConfig = config;
  }

  // 抽象方法：发送消息
  abstract sendMessage(
    messages: LLMMessage[], 
    config?: LLMConfig
  ): Promise<LLMResponse>;

  // 抽象方法：测试连接
  abstract testConnection(): Promise<boolean>;

  // 获取模型信息
  abstract getModelInfo(): {
    name: string;
    provider: string;
    maxTokens: number;
    supportedFeatures: string[];
  };

  // 合并配置
  protected mergeConfig(config?: LLMConfig): LLMConfig {
    return { ...this.baseConfig, ...config };
  }

  // 验证消息格式
  protected validateMessages(messages: LLMMessage[]): void {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new Error('消息列表不能为空');
    }

    for (const message of messages) {
      if (!message.role || !message.content) {
        throw new Error('消息必须包含role和content字段');
      }
      
      if (!['system', 'user', 'assistant'].includes(message.role)) {
        throw new Error('消息角色必须是system、user或assistant');
      }
    }
  }

  // 计算token数量（简单估算）
  protected estimateTokens(text: string): number {
    // 简单的token估算：英文约4字符=1token，中文约1.5字符=1token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  // 处理错误
  protected handleError(error: any): never {
    if (error.response) {
      // HTTP错误
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;
      
      switch (status) {
        case 401:
          throw new Error('API密钥无效或已过期');
        case 403:
          throw new Error('API访问被拒绝，请检查权限');
        case 429:
          throw new Error('API调用频率超限，请稍后重试');
        case 500:
          throw new Error('API服务器内部错误');
        default:
          throw new Error(`API调用失败: ${message}`);
      }
    } else if (error.code === 'ENOTFOUND') {
      throw new Error('网络连接失败，请检查网络设置');
    } else if (error.code === 'ETIMEDOUT') {
      throw new Error('API调用超时，请稍后重试');
    } else {
      throw new Error(`未知错误: ${error.message}`);
    }
  }

  // 重试机制
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (i === maxRetries) {
          break;
        }

        // 如果是认证错误或配置错误，不重试
        if (error instanceof Error && 
            (error.message.includes('API密钥') || 
             error.message.includes('权限') ||
             error.message.includes('配置'))) {
          break;
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }

    throw lastError!;
  }

  // 日志记录
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logData = data ? JSON.stringify(data) : '';
    console.log(`[${timestamp}] [${level.toUpperCase()}] [${this.constructor.name}] ${message} ${logData}`);
  }
}

// LLM提供商工厂
export class LLMProviderFactory {
  private static providers: Map<string, typeof LLMProvider> = new Map();

  // 注册提供商
  static registerProvider(name: string, providerClass: typeof LLMProvider): void {
    this.providers.set(name, providerClass);
  }

  // 创建提供商实例
  static createProvider(
    providerName: string, 
    apiKey: string, 
    model: string, 
    config?: LLMConfig
  ): LLMProvider {
    const ProviderClass = this.providers.get(providerName);
    if (!ProviderClass) {
      throw new Error(`不支持的LLM提供商: ${providerName}`);
    }

    return new ProviderClass(apiKey, model, config);
  }

  // 获取支持的提供商列表
  static getSupportedProviders(): string[] {
    return Array.from(this.providers.keys());
  }
}

// 错误类型
export class LLMError extends Error {
  constructor(
    message: string,
    public provider: string,
    public model: string,
    public code?: string
  ) {
    super(message);
    this.name = 'LLMError';
  }
}

export class LLMRateLimitError extends LLMError {
  constructor(provider: string, model: string, retryAfter?: number) {
    super(`API调用频率超限`, provider, model, 'RATE_LIMIT');
    this.retryAfter = retryAfter;
  }

  retryAfter?: number;
}

export class LLMAuthError extends LLMError {
  constructor(provider: string, model: string) {
    super('API密钥无效或已过期', provider, model, 'AUTH_ERROR');
  }
}

export class LLMNetworkError extends LLMError {
  constructor(provider: string, model: string, originalError: Error) {
    super(`网络连接失败: ${originalError.message}`, provider, model, 'NETWORK_ERROR');
  }
}
