import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';

const router = Router();

// 验证中间件
const validateCreateGame = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('游戏名称长度必须在1-100个字符之间'),
  body('gameMode')
    .isIn(['standard_12', 'advanced_12', 'custom'])
    .withMessage('游戏模式无效'),
  body('aiModels')
    .isArray({ min: 1 })
    .withMessage('至少需要选择一个AI模型')
];

const validateJoinGame = [
  body('playerType')
    .isIn(['ai', 'human'])
    .withMessage('玩家类型无效'),
  body('aiModel')
    .optional()
    .isString()
    .withMessage('AI模型名称必须是字符串')
];

const validateGameAction = [
  body('actionType')
    .isIn(['speak', 'vote', 'skill_use'])
    .withMessage('行动类型无效'),
  body('targetPlayer')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('目标玩家编号必须在1-12之间'),
  body('content')
    .optional()
    .isString()
    .withMessage('内容必须是字符串')
];

const validateGameQuery = [
  query('status')
    .optional()
    .isIn(['waiting', 'playing', 'finished'])
    .withMessage('游戏状态无效'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('每页数量必须在1-50之间')
];

const validateGameId = [
  param('gameId')
    .isUUID()
    .withMessage('游戏ID格式无效')
];

// 处理验证错误
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: '输入验证失败',
        details: errors.array()
      }
    });
  }
  next();
};

// 获取游戏列表
router.get('/', validateGameQuery, handleValidationErrors, async (req, res) => {
  try {
    const { status = 'waiting', page = 1, limit = 10 } = req.query;
    
    // TODO: 实现游戏列表查询逻辑
    
    res.json({
      success: true,
      data: {
        games: [
          {
            id: 'temp-uuid-1',
            name: '测试游戏1',
            status: 'waiting',
            gameMode: 'standard_12',
            currentPlayers: 0,
            maxPlayers: 12,
            createdAt: new Date().toISOString()
          }
        ],
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: 1,
          totalPages: 1
        }
      }
    });
  } catch (error) {
    console.error('获取游戏列表失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_GAMES_FAILED',
        message: '获取游戏列表失败'
      }
    });
  }
});

// 创建游戏
router.post('/', validateCreateGame, handleValidationErrors, async (req, res) => {
  try {
    const { name, gameMode, aiModels } = req.body;
    
    // TODO: 实现创建游戏逻辑
    
    res.status(201).json({
      success: true,
      data: {
        game: {
          id: 'temp-uuid',
          name,
          status: 'waiting',
          gameMode,
          currentPlayers: 0,
          maxPlayers: 12,
          createdAt: new Date().toISOString()
        }
      },
      message: '游戏创建成功'
    });
  } catch (error) {
    console.error('创建游戏失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CREATE_GAME_FAILED',
        message: '创建游戏失败'
      }
    });
  }
});

// 获取游戏详情
router.get('/:gameId', validateGameId, handleValidationErrors, async (req, res) => {
  try {
    const { gameId } = req.params;
    
    // TODO: 实现获取游戏详情逻辑
    
    res.json({
      success: true,
      data: {
        game: {
          id: gameId,
          name: '测试游戏',
          status: 'waiting',
          gameMode: 'standard_12',
          currentPlayers: 0,
          maxPlayers: 12,
          players: [],
          createdAt: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    console.error('获取游戏详情失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_GAME_FAILED',
        message: '获取游戏详情失败'
      }
    });
  }
});

// 加入游戏
router.post('/:gameId/join', validateGameId, validateJoinGame, handleValidationErrors, async (req, res) => {
  try {
    const { gameId } = req.params;
    const { playerType, aiModel } = req.body;
    
    // TODO: 实现加入游戏逻辑
    
    res.json({
      success: true,
      data: {
        player: {
          id: 'temp-player-uuid',
          playerNumber: 1,
          playerType,
          aiModel,
          isAlive: true,
          isCaptain: false
        }
      },
      message: '加入游戏成功'
    });
  } catch (error) {
    console.error('加入游戏失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'JOIN_GAME_FAILED',
        message: '加入游戏失败'
      }
    });
  }
});

// 开始游戏
router.post('/:gameId/start', validateGameId, handleValidationErrors, async (req, res) => {
  try {
    const { gameId } = req.params;
    
    // TODO: 实现开始游戏逻辑
    
    res.json({
      success: true,
      message: '游戏开始成功'
    });
  } catch (error) {
    console.error('开始游戏失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'START_GAME_FAILED',
        message: '开始游戏失败'
      }
    });
  }
});

// 游戏操作
router.post('/:gameId/action', validateGameId, validateGameAction, handleValidationErrors, async (req, res) => {
  try {
    const { gameId } = req.params;
    const { actionType, targetPlayer, content, metadata } = req.body;
    
    // TODO: 实现游戏操作逻辑
    
    res.json({
      success: true,
      data: {
        action: {
          id: 'temp-action-uuid',
          gameId,
          actionType,
          targetPlayer,
          content,
          timestamp: new Date().toISOString()
        }
      },
      message: '操作执行成功'
    });
  } catch (error) {
    console.error('游戏操作失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'GAME_ACTION_FAILED',
        message: '游戏操作失败'
      }
    });
  }
});

// 获取游戏日志
router.get('/:gameId/logs', validateGameId, handleValidationErrors, async (req, res) => {
  try {
    const { gameId } = req.params;
    
    // TODO: 实现获取游戏日志逻辑
    
    res.json({
      success: true,
      data: {
        logs: []
      }
    });
  } catch (error) {
    console.error('获取游戏日志失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_LOGS_FAILED',
        message: '获取游戏日志失败'
      }
    });
  }
});

export default router;
