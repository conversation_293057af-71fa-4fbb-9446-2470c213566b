import { Server, Socket } from 'socket.io';
import { GameManager } from './GameManager';
import { GameAction, SocketEvents } from '../../../shared/types/game';
import { v4 as uuidv4 } from 'uuid';

export class SocketHandler {
  private io: Server;
  private gameManager: GameManager;
  private gameRooms: Map<string, Set<string>> = new Map(); // gameId -> socketIds

  constructor(io: Server) {
    this.io = io;
    this.gameManager = GameManager.getInstance();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      console.log(`🔌 用户连接: ${socket.id}`);

      // 加入游戏房间
      socket.on('game:join', async (data: { gameId: string }) => {
        try {
          await this.handleJoinGame(socket, data.gameId);
        } catch (error) {
          socket.emit('error', {
            code: 'JOIN_GAME_FAILED',
            message: error instanceof Error ? error.message : '加入游戏失败'
          });
        }
      });

      // 离开游戏房间
      socket.on('game:leave', (data: { gameId: string }) => {
        this.handleLeaveGame(socket, data.gameId);
      });

      // 创建游戏
      socket.on('game:create', async (data: {
        name: string;
        gameMode: string;
        aiModels: string[];
      }) => {
        try {
          const gameState = await this.gameManager.createGame(
            data.name,
            data.gameMode
          );
          
          socket.emit('game:created', gameState);
          
          // 自动加入创建的游戏
          await this.handleJoinGame(socket, gameState.id);
        } catch (error) {
          socket.emit('error', {
            code: 'CREATE_GAME_FAILED',
            message: error instanceof Error ? error.message : '创建游戏失败'
          });
        }
      });

      // 开始游戏
      socket.on('game:start', async (data: { gameId: string }) => {
        try {
          const success = await this.gameManager.startGame(data.gameId);
          if (success) {
            const gameState = await this.gameManager.getGameState(data.gameId);
            this.io.to(data.gameId).emit('game:started', gameState);
          }
        } catch (error) {
          socket.emit('error', {
            code: 'START_GAME_FAILED',
            message: error instanceof Error ? error.message : '开始游戏失败'
          });
        }
      });

      // 玩家行动
      socket.on('game:action', async (data: {
        gameId: string;
        actionType: string;
        targetPlayer?: number;
        content?: string;
        metadata?: any;
      }) => {
        try {
          const action: GameAction = {
            id: uuidv4(),
            gameId: data.gameId,
            phase: 'day', // 将由游戏引擎确定
            round: 1, // 将由游戏引擎确定
            actionType: data.actionType as any,
            targetPlayer: data.targetPlayer,
            content: data.content,
            metadata: data.metadata,
            timestamp: new Date().toISOString()
          };

          const success = await this.gameManager.executeAction(data.gameId, action);
          if (success) {
            // 广播行动到房间内所有玩家
            this.io.to(data.gameId).emit('game:action_executed', action);
          }
        } catch (error) {
          socket.emit('error', {
            code: 'ACTION_FAILED',
            message: error instanceof Error ? error.message : '行动执行失败'
          });
        }
      });

      // 获取游戏状态
      socket.on('game:get_state', async (data: { gameId: string }) => {
        try {
          const gameState = await this.gameManager.getGameState(data.gameId);
          socket.emit('game:state_update', gameState);
        } catch (error) {
          socket.emit('error', {
            code: 'GET_STATE_FAILED',
            message: error instanceof Error ? error.message : '获取游戏状态失败'
          });
        }
      });

      // 心跳检测
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: Date.now() });
      });

      // 断开连接处理
      socket.on('disconnect', () => {
        console.log(`🔌 用户断开连接: ${socket.id}`);
        this.handleDisconnect(socket);
      });
    });
  }

  // 处理加入游戏
  private async handleJoinGame(socket: Socket, gameId: string): Promise<void> {
    // 检查游戏是否存在
    const gameState = await this.gameManager.getGameState(gameId);
    if (!gameState) {
      throw new Error('游戏不存在');
    }

    // 加入Socket.io房间
    socket.join(gameId);

    // 记录房间成员
    if (!this.gameRooms.has(gameId)) {
      this.gameRooms.set(gameId, new Set());
    }
    this.gameRooms.get(gameId)!.add(socket.id);

    // 发送当前游戏状态
    socket.emit('game:joined', {
      gameId,
      gameState
    });

    // 通知房间内其他玩家
    socket.to(gameId).emit('player:joined', {
      socketId: socket.id,
      timestamp: new Date().toISOString()
    });

    console.log(`👤 用户 ${socket.id} 加入游戏房间: ${gameId}`);
  }

  // 处理离开游戏
  private handleLeaveGame(socket: Socket, gameId: string): void {
    socket.leave(gameId);

    // 从房间记录中移除
    const room = this.gameRooms.get(gameId);
    if (room) {
      room.delete(socket.id);
      if (room.size === 0) {
        this.gameRooms.delete(gameId);
      }
    }

    // 通知房间内其他玩家
    socket.to(gameId).emit('player:left', {
      socketId: socket.id,
      timestamp: new Date().toISOString()
    });

    console.log(`👤 用户 ${socket.id} 离开游戏房间: ${gameId}`);
  }

  // 处理断开连接
  private handleDisconnect(socket: Socket): void {
    // 从所有游戏房间中移除
    for (const [gameId, room] of this.gameRooms) {
      if (room.has(socket.id)) {
        room.delete(socket.id);
        
        // 通知房间内其他玩家
        socket.to(gameId).emit('player:disconnected', {
          socketId: socket.id,
          timestamp: new Date().toISOString()
        });

        if (room.size === 0) {
          this.gameRooms.delete(gameId);
        }
      }
    }
  }

  // 设置游戏引擎事件监听
  setupGameEngineListeners(gameId: string): void {
    const game = this.gameManager.getGame(gameId);
    if (!game) return;

    // 阶段变化
    game.on('phaseChanged', (data) => {
      this.io.to(gameId).emit('game:phase_change', data);
    });

    // 玩家发言
    game.on('playerSpeak', (data) => {
      this.io.to(gameId).emit('player:speak', data);
    });

    // 玩家投票
    game.on('playerVote', (data) => {
      this.io.to(gameId).emit('player:vote', data);
    });

    // 玩家淘汰
    game.on('playerEliminated', (data) => {
      this.io.to(gameId).emit('player:eliminated', data);
    });

    // 技能使用
    game.on('skillUsed', (data) => {
      this.io.to(gameId).emit('skill:used', data);
    });

    // 夜晚行动处理完成
    game.on('nightActionsProcessed', (data) => {
      this.io.to(gameId).emit('night:actions_processed', data);
    });

    // 预言家查验结果（只发送给预言家）
    game.on('prophetCheck', (data) => {
      // TODO: 找到预言家的socket并发送私密消息
      this.io.to(gameId).emit('prophet:check_result', data);
    });

    // 游戏结束
    game.on('gameEnded', (data) => {
      this.io.to(gameId).emit('game:ended', data);
      
      // 清理房间
      setTimeout(() => {
        this.gameRooms.delete(gameId);
      }, 60000); // 1分钟后清理
    });
  }

  // 广播系统消息
  broadcastSystemMessage(gameId: string, message: string): void {
    this.io.to(gameId).emit('system:message', {
      message,
      timestamp: new Date().toISOString()
    });
  }

  // 获取房间内连接数
  getRoomConnectionCount(gameId: string): number {
    return this.gameRooms.get(gameId)?.size || 0;
  }

  // 获取所有活跃房间
  getActiveRooms(): string[] {
    return Array.from(this.gameRooms.keys());
  }
}
