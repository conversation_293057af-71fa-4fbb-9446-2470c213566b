-- 数据库初始化脚本
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    avatar_url VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建游戏表
CREATE TABLE IF NOT EXISTS games (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'waiting', -- waiting, playing, finished
    game_mode VARCHAR(50) DEFAULT 'standard_12', -- standard_12, advanced_12, etc.
    max_players INTEGER DEFAULT 12,
    current_players INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    winner_team VARCHAR(20), -- werewolf, villager, third_party
    game_data JSONB -- 存储游戏状态和配置
);

-- 创建游戏玩家表
CREATE TABLE IF NOT EXISTS game_players (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    player_number INTEGER NOT NULL, -- 1-12
    player_type VARCHAR(20) DEFAULT 'ai', -- ai, human
    ai_model VARCHAR(50), -- openai_gpt4, claude_3, gemini_pro, etc.
    role VARCHAR(20), -- werewolf, villager, prophet, witch, hunter, etc.
    is_alive BOOLEAN DEFAULT true,
    is_captain BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(game_id, player_number)
);

-- 创建游戏日志表
CREATE TABLE IF NOT EXISTS game_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    phase VARCHAR(20) NOT NULL, -- night, day, vote, etc.
    round_number INTEGER NOT NULL,
    action_type VARCHAR(50) NOT NULL, -- speak, vote, skill_use, etc.
    player_number INTEGER,
    target_player INTEGER,
    content TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建AI模型配置表
CREATE TABLE IF NOT EXISTS ai_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL, -- openai, anthropic, google, etc.
    model_id VARCHAR(100) NOT NULL,
    api_endpoint VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    default_config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户API密钥表
CREATE TABLE IF NOT EXISTS user_api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    api_key_encrypted TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, provider)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
CREATE INDEX IF NOT EXISTS idx_games_created_at ON games(created_at);
CREATE INDEX IF NOT EXISTS idx_game_players_game_id ON game_players(game_id);
CREATE INDEX IF NOT EXISTS idx_game_logs_game_id ON game_logs(game_id);
CREATE INDEX IF NOT EXISTS idx_game_logs_created_at ON game_logs(created_at);

-- 插入默认AI模型配置
INSERT INTO ai_models (name, display_name, provider, model_id, default_config) VALUES
('gpt-4', 'GPT-4', 'openai', 'gpt-4', '{"temperature": 0.8, "max_tokens": 1000}'),
('gpt-3.5-turbo', 'GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', '{"temperature": 0.8, "max_tokens": 1000}'),
('claude-3-opus', 'Claude 3 Opus', 'anthropic', 'claude-3-opus-20240229', '{"temperature": 0.8, "max_tokens": 1000}'),
('claude-3-sonnet', 'Claude 3 Sonnet', 'anthropic', 'claude-3-sonnet-20240229', '{"temperature": 0.8, "max_tokens": 1000}'),
('gemini-pro', 'Gemini Pro', 'google', 'gemini-pro', '{"temperature": 0.8, "maxOutputTokens": 1000}'),
('qwen2.5-7b', 'Qwen2.5-7B', 'siliconflow', 'Qwen/Qwen2.5-7B-Instruct', '{"temperature": 0.8, "max_tokens": 1000}'),
('qwen2.5-14b', 'Qwen2.5-14B', 'siliconflow', 'Qwen/Qwen2.5-14B-Instruct', '{"temperature": 0.8, "max_tokens": 1000}'),
('glm-4-9b', 'GLM-4-9B', 'siliconflow', 'THUDM/glm-4-9b-chat', '{"temperature": 0.8, "max_tokens": 1000}'),
('llama-3.1-8b', 'Llama-3.1-8B', 'siliconflow', 'meta-llama/Meta-Llama-3.1-8B-Instruct', '{"temperature": 0.8, "max_tokens": 1000}')
ON CONFLICT (name) DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_api_keys_updated_at BEFORE UPDATE ON user_api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
